"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Network,
  Server,
  Code
} from "lucide-react"
import apiService from "@/lib/api"
import { config } from "@/lib/config"

export default function ApiTest() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [customUrl, setCustomUrl] = useState('/api/movement/pursuit-ability/page')
  const [customData, setCustomData] = useState('{\n  "current": 1,\n  "size": 10\n}')

  // 测试注视稳定性列表API
  const testGazeStabilityList = async () => {
    setLoading(true)
    setError(null)
    setResult(null)
    
    try {
      const response = await apiService.getGazeStabilityList({
        current: 1,
        size: 10
      })
      setResult(response)
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败')
    } finally {
      setLoading(false)
    }
  }

  // 测试注视稳定性详情API
  const testGazeStabilityDetail = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await apiService.getGazeStabilityDetail(30)
      setResult(response)
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败')
    } finally {
      setLoading(false)
    }
  }

  // 测试追随能力列表API
  const testPursuitAbilityList = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await apiService.getPursuitAbilityList({
        current: 1,
        size: 10
      })
      setResult(response)
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败')
    } finally {
      setLoading(false)
    }
  }

  // 测试追随能力详情API
  const testPursuitAbilityDetail = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await apiService.getPursuitAbilityDetail(30)
      setResult(response)
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败')
    } finally {
      setLoading(false)
    }
  }

  // 测试自定义请求
  const testCustomRequest = async () => {
    setLoading(true)
    setError(null)
    setResult(null)
    
    try {
      let data = null
      if (customData.trim()) {
        data = JSON.parse(customData)
      }
      
      const response = await apiService.post(customUrl, data)
      setResult(response)
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败')
    } finally {
      setLoading(false)
    }
  }

  // 测试代理连接
  const testProxyConnection = async () => {
    setLoading(true)
    setError(null)
    setResult(null)
    
    try {
      const response = await fetch('/api/proxy/api/movement/gaze-stability/page', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          current: 1,
          size: 5
        })
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      setResult(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : '代理连接失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">API 测试工具</h1>
          <p className="text-muted-foreground">
            测试API连接和代理配置
          </p>
        </div>
      </div>

      {/* 配置信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Server className="h-5 w-5" />
            <span>当前配置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">API 基础URL</Label>
              <p className="text-sm bg-muted p-2 rounded">{config.api.baseURL}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">代理URL</Label>
              <p className="text-sm bg-muted p-2 rounded">/api/proxy</p>
            </div>
            <div>
              <Label className="text-sm font-medium">超时时间</Label>
              <p className="text-sm bg-muted p-2 rounded">{config.api.timeout}ms</p>
            </div>
            <div>
              <Label className="text-sm font-medium">环境</Label>
              <Badge variant={config.dev.isDevelopment ? "secondary" : "default"}>
                {config.dev.isDevelopment ? "开发环境" : "生产环境"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 测试选项 */}
      <Tabs defaultValue="predefined" className="space-y-4">
        <TabsList>
          <TabsTrigger value="predefined">预定义测试</TabsTrigger>
          <TabsTrigger value="custom">自定义测试</TabsTrigger>
          <TabsTrigger value="proxy">代理测试</TabsTrigger>
        </TabsList>

        <TabsContent value="predefined" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Play className="h-5 w-5" />
                <span>预定义API测试</span>
              </CardTitle>
              <CardDescription>
                测试常用的API接口
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">注视稳定性测试</h4>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      onClick={testGazeStabilityList}
                      disabled={loading}
                      className="flex items-center space-x-2"
                    >
                      {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Network className="h-4 w-4" />}
                      <span>测试注视稳定性列表</span>
                    </Button>
                    <Button
                      onClick={testGazeStabilityDetail}
                      disabled={loading}
                      variant="outline"
                      className="flex items-center space-x-2"
                    >
                      {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Network className="h-4 w-4" />}
                      <span>测试注视稳定性详情</span>
                    </Button>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-2">追随能力测试</h4>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      onClick={testPursuitAbilityList}
                      disabled={loading}
                      className="flex items-center space-x-2"
                    >
                      {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Network className="h-4 w-4" />}
                      <span>测试追随能力列表</span>
                    </Button>
                    <Button
                      onClick={testPursuitAbilityDetail}
                      disabled={loading}
                      variant="outline"
                      className="flex items-center space-x-2"
                    >
                      {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Network className="h-4 w-4" />}
                      <span>测试追随能力详情</span>
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="custom" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="h-5 w-5" />
                <span>自定义API测试</span>
              </CardTitle>
              <CardDescription>
                自定义URL和请求数据
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="custom-url">请求URL</Label>
                <Input
                  id="custom-url"
                  value={customUrl}
                  onChange={(e) => setCustomUrl(e.target.value)}
                  placeholder="/api/movement/gaze-stability/page"
                />
              </div>
              <div>
                <Label htmlFor="custom-data">请求数据 (JSON)</Label>
                <Textarea
                  id="custom-data"
                  value={customData}
                  onChange={(e) => setCustomData(e.target.value)}
                  placeholder='{"current": 1, "size": 10}'
                  rows={6}
                />
              </div>
              <Button 
                onClick={testCustomRequest} 
                disabled={loading}
                className="flex items-center space-x-2"
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
                <span>发送请求</span>
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="proxy" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Network className="h-5 w-5" />
                <span>代理连接测试</span>
              </CardTitle>
              <CardDescription>
                直接测试代理路由
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={testProxyConnection} 
                disabled={loading}
                className="flex items-center space-x-2"
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Network className="h-4 w-4" />}
                <span>测试代理连接</span>
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 结果显示 */}
      {error && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>请求失败:</strong> {error}
          </AlertDescription>
        </Alert>
      )}

      {result && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>请求成功!</strong> 查看下方详细结果
          </AlertDescription>
        </Alert>
      )}

      {result && (
        <Card>
          <CardHeader>
            <CardTitle>响应结果</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm">
              {JSON.stringify(result, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
