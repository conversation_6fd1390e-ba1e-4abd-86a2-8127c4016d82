/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/proxy/[...path]/route";
exports.ids = ["app/api/proxy/[...path]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/proxy/[...path]/route.ts":
/*!******************************************!*\
  !*** ./app/api/proxy/[...path]/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// 目标服务器地址，从环境变量读取，默认为localhost:8080\nconst TARGET_URL = \"http://localhost:8080\" || 0;\n// 处理所有HTTP方法的代理请求\nasync function handleProxy(request, { params }) {\n    try {\n        // 构建目标URL\n        const path = params.path.join('/');\n        const targetUrl = `${TARGET_URL}/${path}`;\n        // 获取查询参数\n        const searchParams = request.nextUrl.searchParams;\n        const queryString = searchParams.toString();\n        const fullUrl = queryString ? `${targetUrl}?${queryString}` : targetUrl;\n        // 获取请求体\n        let body = null;\n        if (request.method !== 'GET' && request.method !== 'HEAD') {\n            try {\n                body = await request.text();\n            } catch (error) {\n                console.error('Error reading request body:', error);\n            }\n        }\n        // 构建请求头，排除一些不需要的头部\n        const headers = {};\n        request.headers.forEach((value, key)=>{\n            // 排除一些可能导致问题的头部\n            if (![\n                'host',\n                'connection',\n                'content-length'\n            ].includes(key.toLowerCase())) {\n                headers[key] = value;\n            }\n        });\n        // 确保Content-Type头部存在\n        if (body && !headers['content-type']) {\n            headers['content-type'] = 'application/json';\n        }\n        console.log(`Proxying ${request.method} request to: ${fullUrl}`);\n        if (body) {\n            console.log('Request body:', body);\n        }\n        // 发送代理请求\n        const response = await fetch(fullUrl, {\n            method: request.method,\n            headers,\n            body: body || undefined\n        });\n        console.log(`Response status: ${response.status} ${response.statusText}`);\n        // 获取响应数据\n        const responseData = await response.text();\n        // 记录响应数据（仅在开发环境）\n        if (true) {\n            console.log('Response data:', responseData);\n        }\n        // 构建响应头，添加CORS头部\n        const responseHeaders = new Headers();\n        // 复制原始响应头\n        response.headers.forEach((value, key)=>{\n            responseHeaders.set(key, value);\n        });\n        // 添加CORS头部\n        responseHeaders.set('Access-Control-Allow-Origin', '*');\n        responseHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');\n        responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');\n        responseHeaders.set('Access-Control-Max-Age', '86400');\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(responseData, {\n            status: response.status,\n            statusText: response.statusText,\n            headers: responseHeaders\n        });\n    } catch (error) {\n        console.error('Proxy error details:', {\n            error: error instanceof Error ? error.message : error,\n            stack: error instanceof Error ? error.stack : undefined,\n            targetUrl: TARGET_URL,\n            path: params.path.join('/'),\n            method: request.method\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Proxy request failed',\n            message: error instanceof Error ? error.message : 'Unknown error',\n            details: {\n                targetUrl: TARGET_URL,\n                path: params.path.join('/'),\n                method: request.method\n            },\n            timestamp: Date.now()\n        }, {\n            status: 500,\n            headers: {\n                'Access-Control-Allow-Origin': '*',\n                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n                'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'\n            }\n        });\n    }\n}\n// 处理OPTIONS请求（预检请求）\nasync function OPTIONS(request, context) {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',\n            'Access-Control-Max-Age': '86400'\n        }\n    });\n}\n// 导出所有HTTP方法的处理函数\nasync function GET(request, context) {\n    return handleProxy(request, context);\n}\nasync function POST(request, context) {\n    return handleProxy(request, context);\n}\nasync function PUT(request, context) {\n    return handleProxy(request, context);\n}\nasync function DELETE(request, context) {\n    return handleProxy(request, context);\n}\nasync function PATCH(request, context) {\n    return handleProxy(request, context);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/proxy/[...path]/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&page=%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproxy%2F%5B...path%5D%2Froute.ts&appDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&page=%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproxy%2F%5B...path%5D%2Froute.ts&appDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_research_ui_v0_eye_tracking_system_app_api_proxy_path_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/proxy/[...path]/route.ts */ \"(rsc)/./app/api/proxy/[...path]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/proxy/[...path]/route\",\n        pathname: \"/api/proxy/[...path]\",\n        filename: \"route\",\n        bundlePath: \"app/api/proxy/[...path]/route\"\n    },\n    resolvedPagePath: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\app\\\\api\\\\proxy\\\\[...path]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_research_ui_v0_eye_tracking_system_app_api_proxy_path_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&page=%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproxy%2F%5B...path%5D%2Froute.ts&appDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&page=%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproxy%2F%5B...path%5D%2Froute.ts&appDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();