"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  User,
  Calendar,
  Activity,
  Target,
  Eye,
  Settings,
  FileText,
  BarChart3,
  Home,
  Move,
  TrendingUp,
  Gauge
} from "lucide-react"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import apiService, { PursuitAbilityRecord, PursuitPoint } from "@/lib/api"
import BezierErrorVisualization from "./bezier-error-visualization"

interface PursuitAbilityDetailProps {
  recordId: number
  onBack?: () => void
}

export default function PursuitAbilityDetail({ recordId, onBack }: PursuitAbilityDetailProps) {
  const [record, setRecord] = useState<PursuitAbilityRecord | null>(null)
  const [pursuitPoints, setPursuitPoints] = useState<PursuitPoint[]>([])
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  // 获取详情数据
  const fetchDetail = async () => {
    setLoading(true)
    try {
      const response = await apiService.getPursuitAbilityDetail(recordId)
      setRecord(response.data)
      
      // 解析追随轨迹数据
      if (response.data.gazeTrajectoryJson) {
        try {
          const points = JSON.parse(response.data.gazeTrajectoryJson)
          // 确保解析的数据是数组
          if (Array.isArray(points)) {
            setPursuitPoints(points as PursuitPoint[])
          } else {
            console.error('追随轨迹数据不是数组格式:', points)
            setPursuitPoints([])
          }
        } catch (error) {
          console.error('解析追随轨迹数据失败:', error)
          setPursuitPoints([])
        }
      } else {
        setPursuitPoints([])
      }
    } catch (error) {
      toast({
        title: "获取详情失败",
        description: error instanceof Error ? error.message : "请检查网络连接",
        variant: "destructive",
      })
      console.error('获取追随能力测试详情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (recordId) {
      fetchDetail()
    }
  }, [recordId])

  // 状态颜色映射
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'PROCESSING':
        return 'bg-blue-100 text-blue-800'
      case 'FAILED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 格式化时长
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 返回处理
  const handleBack = () => {
    if (onBack) {
      onBack()
    } else {
      router.back()
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        <span className="ml-2">加载中...</span>
      </div>
    )
  }

  if (!record) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">未找到测试记录</p>
        <Button onClick={handleBack} className="mt-4">
          返回列表
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={handleBack} className="flex items-center gap-2">
            <Home className="w-4 h-4" />
            返回列表
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <Move className="w-6 h-6 text-green-600" />
              追随能力测试详情
            </h1>
            <p className="text-gray-600 mt-1">测试序号: {record.testSequence}</p>
          </div>
        </div>
        <Badge className={getStatusColor(record.status)}>
          {record.statusDesc}
        </Badge>
      </div>

      {/* 基本信息卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center gap-2">
              <User className="w-4 h-4 text-green-600" />
              患者信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">姓名</span>
              <span className="font-medium">{record.patientName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">住院号</span>
              <span className="font-medium">{record.inpatientNum}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">病历号</span>
              <span className="font-medium">{record.caseCardNum}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center gap-2">
              <Calendar className="w-4 h-4 text-green-600" />
              测试信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">测试日期</span>
              <span className="font-medium">
                {new Date(record.testDate).toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">测试时间</span>
              <span className="font-medium">
                {new Date(record.testDate).toLocaleTimeString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">测试时长</span>
              <span className="font-medium">{formatDuration(record.duration)}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center gap-2">
              <Activity className="w-4 h-4 text-green-600" />
              设备信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">设备名称</span>
              <span className="font-medium">{record.deviceName || '未知设备'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">设备序列号</span>
              <span className="font-medium text-xs">{record.deviceSn}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">操作员</span>
              <span className="font-medium">{record.operatorName || '未知'}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center gap-2">
              <BarChart3 className="w-4 h-4 text-green-600" />
              测试结果
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">追随精度</span>
              <span className="font-medium text-green-600">
                {record.pursuitAccuracy?.toFixed(1)}%
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">平均速度</span>
              <span className="font-medium">{record.averageVelocity?.toFixed(1)} °/s</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">速度增益</span>
              <span className="font-medium">{record.velocityGain?.toFixed(2)}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细信息标签页 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            测试概览
          </TabsTrigger>
          <TabsTrigger value="trajectory" className="flex items-center gap-2">
            <Target className="w-4 h-4" />
            追随轨迹
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            测试配置
          </TabsTrigger>
          <TabsTrigger value="notes" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            备注信息
          </TabsTrigger>
        </TabsList>

        {/* 测试概览 */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center gap-2">
                  <Move className="w-4 h-4 text-green-600" />
                  追随统计
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">总追随时间</span>
                  <span className="font-medium">{formatDuration(record.totalPursuitTime)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">有效追随时间</span>
                  <span className="font-medium text-green-600">{formatDuration(record.effectivePursuitTime)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">追随精度</span>
                  <span className="font-medium">{record.pursuitAccuracy?.toFixed(1)}%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center gap-2">
                  <Gauge className="w-4 h-4 text-blue-600" />
                  速度分析
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">平均速度</span>
                  <span className="font-medium">{record.averageVelocity?.toFixed(1)} °/s</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">速度增益</span>
                  <span className="font-medium">{record.velocityGain?.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">相位延迟</span>
                  <span className="font-medium">{record.phaseDelay?.toFixed(1)} ms</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center gap-2">
                  <TrendingUp className="w-4 h-4 text-purple-600" />
                  质量指标
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">平滑度指数</span>
                  <span className="font-medium">{record.smoothnessIndex?.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">跟踪误差</span>
                  <span className="font-medium">{record.trackingError?.toFixed(2)} px</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">追随延迟</span>
                  <span className="font-medium">{record.pursuitLatency?.toFixed(1)} ms</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 贝塞尔曲线误差评估 */}
          {record.followPathPoints && record.actualGazePoints && (
            <BezierErrorVisualization
              followPathPoints={record.followPathPoints}
              actualGazePoints={record.actualGazePoints}
              screenWidth={1920}
              screenHeight={1080}
            />
          )}
        </TabsContent>

        {/* 追随轨迹 */}
        <TabsContent value="trajectory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                追随轨迹可视化
              </CardTitle>
              <CardDescription>
                显示眼球追随目标的运动轨迹和质量分析
              </CardDescription>
            </CardHeader>
            <CardContent>
              {record.imageUrl ? (
                <div className="space-y-4">
                  <div className="border rounded-lg overflow-hidden">
                    <img
                      src={record.imageUrl}
                      alt="追随轨迹图"
                      className="w-full h-auto"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none'
                        e.currentTarget.nextElementSibling?.classList.remove('hidden')
                      }}
                    />
                    <div className="hidden p-8 text-center text-gray-500">
                      <Eye className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                      <p>轨迹图像加载失败</p>
                    </div>
                  </div>

                  {/* 轨迹数据统计 */}
                  {pursuitPoints.length > 0 && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base">轨迹统计</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">轨迹点数</span>
                            <span className="font-medium">{pursuitPoints.length}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">平均速度</span>
                            <span className="font-medium">
                              {(pursuitPoints.reduce((sum, p) => sum + p.velocity, 0) / pursuitPoints.length).toFixed(1)} °/s
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">平均误差</span>
                            <span className="font-medium">
                              {(pursuitPoints.reduce((sum, p) => sum + p.trackingError, 0) / pursuitPoints.length).toFixed(2)} px
                            </span>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base">质量分析</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">目标命中率</span>
                            <span className="font-medium text-green-600">
                              {((pursuitPoints.filter(p => p.isOnTarget).length / pursuitPoints.length) * 100).toFixed(1)}%
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">平均增益</span>
                            <span className="font-medium">
                              {(pursuitPoints.reduce((sum, p) => sum + p.velocityGain, 0) / pursuitPoints.length).toFixed(2)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">平均延迟</span>
                            <span className="font-medium">
                              {(pursuitPoints.reduce((sum, p) => sum + p.phaseDelay, 0) / pursuitPoints.length).toFixed(1)} ms
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Eye className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                  <p>暂无轨迹图像</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 测试配置 */}
        <TabsContent value="settings" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  校准参数
                </CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-sm bg-gray-50 p-3 rounded border overflow-auto">
                  {record.calibrationParams || '无校准参数'}
                </pre>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  环境信息
                </CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-sm bg-gray-50 p-3 rounded border overflow-auto">
                  {record.environmentInfo || '无环境信息'}
                </pre>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 备注信息 */}
        <TabsContent value="notes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                测试备注
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded border min-h-32">
                {record.notes ? (
                  <p className="text-sm whitespace-pre-wrap">{record.notes}</p>
                ) : (
                  <p className="text-gray-500 text-sm">无备注信息</p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
