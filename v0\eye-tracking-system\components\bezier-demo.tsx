"use client"

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Target, Home } from "lucide-react"
import { useRouter } from "next/navigation"
import BezierErrorVisualization from "./bezier-error-visualization"

export default function BezierDemo() {
  const router = useRouter()

  // 示例数据 - 基于您提供的API响应格式
  const exampleFollowPathPoints = JSON.stringify([
    [
      [0.052, 0.185], [0.156, 0.370], [0.260, 0.278], [0.365, 0.463]
    ]
  ])

  const exampleActualGazePoints = JSON.stringify([
    [
      [0.053, 0.184], [0.155, 0.372], [0.262, 0.277], [0.362, 0.461]
    ]
  ])

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <Target className="h-8 w-8 text-blue-600" />
            贝塞尔曲线误差评估演示
          </h1>
          <p className="text-gray-600 mt-1">展示目标轨迹与实际注视轨迹的误差分析功能</p>
        </div>
        <Button variant="outline" onClick={() => router.push('/')} className="flex items-center gap-2">
          <Home className="h-4 w-4" />
          返回主页
        </Button>
      </div>

      {/* 功能说明 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>功能说明</CardTitle>
          <CardDescription>
            贝塞尔曲线误差评估组件可以分析眼动追踪中目标轨迹与实际注视轨迹的差异
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="p-3 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">误差统计</h4>
              <p className="text-blue-700">计算平均误差、最大误差、RMS误差等统计指标</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">轨迹可视化</h4>
              <p className="text-green-700">直观显示目标点、实际注视点和误差方向</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg">
              <h4 className="font-semibold text-purple-800 mb-2">分布分析</h4>
              <p className="text-purple-700">9宫格区域分布热力图，分析视线分布特征</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 演示组件 */}
      <BezierErrorVisualization
        followPathPoints={exampleFollowPathPoints}
        actualGazePoints={exampleActualGazePoints}
        screenWidth={1920}
        screenHeight={1080}
      />

      {/* 数据说明 */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>数据格式说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">目标路径点 (followPathPoints)</h4>
              <pre className="text-xs bg-gray-100 p-3 rounded border overflow-auto">
{`[
  [
    [0.052, 0.185], // [x, y] 归一化坐标
    [0.156, 0.370],
    [0.260, 0.278],
    [0.365, 0.463]
  ]
]`}
              </pre>
            </div>
            <div>
              <h4 className="font-semibold mb-2">实际注视点 (actualGazePoints)</h4>
              <pre className="text-xs bg-gray-100 p-3 rounded border overflow-auto">
{`[
  [
    [0.053, 0.184], // [x, y] 归一化坐标
    [0.155, 0.372],
    [0.262, 0.277],
    [0.362, 0.461]
  ]
]`}
              </pre>
            </div>
          </div>
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              <strong>注意：</strong> 坐标数据为归一化格式 (0-1)，组件会自动转换为屏幕像素坐标进行计算和显示。
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
