"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, Search, Eye, FileText, Clock, User, Activity, Zap } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import apiService, { SaccadeAbilityRecord, PageResponse, PageParams } from "@/lib/api"

interface SaccadeAbilityListProps {
  onViewDetail?: (record: SaccadeAbilityRecord) => void
}

export default function SaccadeAbilityList({ onViewDetail }: SaccadeAbilityListProps) {
  const [records, setRecords] = useState<SaccadeAbilityRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    size: 10,
    total: 0,
    pages: 0
  })
  
  // 搜索参数
  const [searchParams, setSearchParams] = useState<PageParams>({
    current: 1,
    size: 10,
    patientName: '',
    status: '',
  })

  const { toast } = useToast()

  // 获取数据
  const fetchData = async (params: PageParams = searchParams) => {
    setLoading(true)
    try {
      const response = await apiService.getSaccadeAbilityList(params)
      setRecords(response.data.records)
      setPagination({
        current: response.data.current,
        size: response.data.size,
        total: response.data.total,
        pages: response.data.pages
      })
    } catch (error) {
      toast({
        title: "获取数据失败",
        description: error instanceof Error ? error.message : "请检查网络连接",
        variant: "destructive",
      })
      console.error('获取扫视能力测试数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 初始化数据
  useEffect(() => {
    fetchData()
  }, [])

  // 搜索处理
  const handleSearch = () => {
    const newParams = { ...searchParams, current: 1 }
    setSearchParams(newParams)
    fetchData(newParams)
  }

  // 重置搜索
  const handleReset = () => {
    const resetParams = {
      current: 1,
      size: 10,
      patientName: '',
      status: '',
    }
    setSearchParams(resetParams)
    fetchData(resetParams)
  }

  // 分页处理
  const handlePageChange = (page: number) => {
    const newParams = { ...searchParams, current: page }
    setSearchParams(newParams)
    fetchData(newParams)
  }

  // 页面大小变化
  const handlePageSizeChange = (size: number) => {
    const newParams = { ...searchParams, size, current: 1 }
    setSearchParams(newParams)
    fetchData(newParams)
  }

  // 状态颜色映射
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge variant="default" className="bg-green-100 text-green-800">已完成</Badge>
      case 'IN_PROGRESS':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">进行中</Badge>
      case 'FAILED':
        return <Badge variant="destructive">失败</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  // 格式化时长
  const formatDuration = (duration: number) => {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和统计 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">扫视能力测试</h1>
          <p className="text-muted-foreground">
            管理和查看患者的扫视能力测试记录
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <Card className="px-4 py-2">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm font-medium">总测试数</p>
                <p className="text-2xl font-bold text-purple-600">{pagination.total}</p>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            搜索筛选
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 items-end">
            <div className="flex-1 min-w-[200px]">
              <label className="text-sm font-medium mb-2 block">患者姓名</label>
              <Input
                placeholder="输入患者姓名"
                value={searchParams.patientName || ''}
                onChange={(e) => setSearchParams(prev => ({ ...prev, patientName: e.target.value }))}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <div className="min-w-[150px]">
              <label className="text-sm font-medium mb-2 block">测试状态</label>
              <Select
                value={searchParams.status || 'ALL'}
                onValueChange={(value) => setSearchParams(prev => ({ ...prev, status: value === 'ALL' ? '' : value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">全部状态</SelectItem>
                  <SelectItem value="COMPLETED">已完成</SelectItem>
                  <SelectItem value="IN_PROGRESS">进行中</SelectItem>
                  <SelectItem value="FAILED">失败</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Button onClick={handleSearch} disabled={loading}>
                <Search className="w-4 h-4 mr-2" />
                搜索
              </Button>
              <Button variant="outline" onClick={handleReset} disabled={loading}>
                重置
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 数据表格 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            测试记录列表
          </CardTitle>
          <CardDescription>
            共 {pagination.total} 条记录，第 {pagination.current} / {pagination.pages} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
              <span className="ml-2">加载中...</span>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>患者信息</TableHead>
                      <TableHead>测试序号</TableHead>
                      <TableHead>测试时间</TableHead>
                      <TableHead>时长</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>准确率</TableHead>
                      <TableHead>平均扫视时间</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {records.map((record) => (
                      <TableRow key={record.id} className="hover:bg-gray-50">
                        <TableCell>
                          <div>
                            <div className="font-medium">{record.patientName}</div>
                            <div className="text-sm text-gray-500">
                              住院号: {record.inpatientNum}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-mono text-sm">{record.testSequence}</div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 text-sm">
                            <Calendar className="w-3 h-3" />
                            {formatDate(record.testDate)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 text-sm">
                            <Clock className="w-3 h-3" />
                            {formatDuration(record.duration)}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(record.status)}
                        </TableCell>
                        <TableCell>
                          <span className={`font-medium ${
                            record.accuracyRate >= 80 ? 'text-green-600' :
                            record.accuracyRate >= 60 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {record.accuracyRate.toFixed(1)}%
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">
                            {record.averageSaccadeTime.toFixed(1)}ms
                          </span>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onViewDetail?.(record)}
                            className="flex items-center gap-1"
                          >
                            <Eye className="w-3 h-3" />
                            查看详情
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* 分页控件 */}
              {pagination.pages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">每页显示</span>
                    <Select
                      value={pagination.size.toString()}
                      onValueChange={(value) => handlePageSizeChange(parseInt(value))}
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                      </SelectContent>
                    </Select>
                    <span className="text-sm text-gray-600">条</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={pagination.current <= 1}
                      onClick={() => handlePageChange(pagination.current - 1)}
                    >
                      上一页
                    </Button>
                    <span className="text-sm">
                      第 {pagination.current} / {pagination.pages} 页
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={pagination.current >= pagination.pages}
                      onClick={() => handlePageChange(pagination.current + 1)}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
