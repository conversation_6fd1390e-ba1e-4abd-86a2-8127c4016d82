"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Target,
  Activity,
  BarChart3,
  Grid3X3,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Play,
  FileText,
  Download,
  Home
} from "lucide-react"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import apiService, { 
  BezierErrorEvaluationRequest, 
  BezierErrorEvaluationResponse,
  BezierPoint,
  PointError,
  GridStatistics
} from "@/lib/api"

interface BezierErrorEvaluationProps {
  onBack?: () => void
}

export default function BezierErrorEvaluation({ onBack }: BezierErrorEvaluationProps) {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<BezierErrorEvaluationResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("input")
  
  // 表单数据
  const [screenWidth, setScreenWidth] = useState(1920)
  const [screenHeight, setScreenHeight] = useState(1080)
  const [enableGridStatistics, setEnableGridStatistics] = useState(true)
  const [targetPointsJson, setTargetPointsJson] = useState('')
  const [actualGazePointsJson, setActualGazePointsJson] = useState('')

  const router = useRouter()
  const { toast } = useToast()

  // 默认示例数据
  const defaultTargetPoints = [
    { x: 100.5, y: 200.3, timestamp: 1640995200000, isValid: true },
    { x: 300.8, y: 400.2, timestamp: 1640995201000, isValid: true },
    { x: 500.1, y: 300.7, timestamp: 1640995202000, isValid: true },
    { x: 700.4, y: 500.9, timestamp: 1640995203000, isValid: true }
  ]

  const defaultActualGazePoints = [
    { x: 102.3, y: 198.7, timestamp: 1640995200100, isValid: true },
    { x: 298.5, y: 402.1, timestamp: 1640995201100, isValid: true },
    { x: 503.2, y: 299.4, timestamp: 1640995202100, isValid: true },
    { x: 695.8, y: 498.6, timestamp: 1640995203100, isValid: true }
  ]

  // 加载示例数据
  const loadExampleData = () => {
    setTargetPointsJson(JSON.stringify(defaultTargetPoints, null, 2))
    setActualGazePointsJson(JSON.stringify(defaultActualGazePoints, null, 2))
    toast({
      title: "示例数据已加载",
      description: "您可以直接运行评估或修改数据后再运行",
    })
  }

  // 执行贝塞尔曲线误差评估
  const runEvaluation = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      // 解析JSON数据
      let targetPoints: BezierPoint[]
      let actualGazePoints: BezierPoint[]

      try {
        targetPoints = JSON.parse(targetPointsJson || '[]')
        actualGazePoints = JSON.parse(actualGazePointsJson || '[]')
      } catch (parseError) {
        throw new Error('JSON数据格式错误，请检查输入数据')
      }

      // 验证数据
      if (!Array.isArray(targetPoints) || targetPoints.length === 0) {
        throw new Error('目标点数据不能为空')
      }
      if (!Array.isArray(actualGazePoints) || actualGazePoints.length === 0) {
        throw new Error('实际注视点数据不能为空')
      }

      const requestData: BezierErrorEvaluationRequest = {
        targetPoints,
        actualGazePoints,
        screenWidth,
        screenHeight,
        enableGridStatistics
      }

      const response = await apiService.evaluateBezierError(requestData)
      setResult(response.data)
      setActiveTab("results")
      
      toast({
        title: "评估完成",
        description: "贝塞尔曲线误差评估已成功完成",
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '评估失败'
      setError(errorMessage)
      toast({
        title: "评估失败",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 返回主页
  const handleBackToHome = () => {
    if (onBack) {
      onBack()
    } else {
      router.push('/')
    }
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <Target className="h-8 w-8 text-blue-600" />
            贝塞尔曲线误差评估
          </h1>
          <p className="text-gray-600 mt-1">分析目标轨迹与实际注视轨迹的误差和分布特征</p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={handleBackToHome} className="flex items-center gap-2">
            <Home className="h-4 w-4" />
            返回主页
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="input" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            数据输入
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2" disabled={!result}>
            <BarChart3 className="w-4 h-4" />
            评估结果
          </TabsTrigger>
          <TabsTrigger value="visualization" className="flex items-center gap-2" disabled={!result}>
            <Grid3X3 className="w-4 h-4" />
            可视化分析
          </TabsTrigger>
        </TabsList>

        {/* 数据输入标签页 */}
        <TabsContent value="input" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 配置参数 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  评估配置
                </CardTitle>
                <CardDescription>设置屏幕参数和评估选项</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="screenWidth">屏幕宽度 (px)</Label>
                    <Input
                      id="screenWidth"
                      type="number"
                      value={screenWidth}
                      onChange={(e) => setScreenWidth(Number(e.target.value))}
                      placeholder="1920"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="screenHeight">屏幕高度 (px)</Label>
                    <Input
                      id="screenHeight"
                      type="number"
                      value={screenHeight}
                      onChange={(e) => setScreenHeight(Number(e.target.value))}
                      placeholder="1080"
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="enableGridStatistics"
                    checked={enableGridStatistics}
                    onCheckedChange={setEnableGridStatistics}
                  />
                  <Label htmlFor="enableGridStatistics">启用网格统计分析</Label>
                </div>
                <div className="pt-4">
                  <Button onClick={loadExampleData} variant="outline" className="w-full">
                    加载示例数据
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 执行评估 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Play className="h-5 w-5" />
                  执行评估
                </CardTitle>
                <CardDescription>运行贝塞尔曲线误差评估分析</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {error && (
                  <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <span className="text-red-700 text-sm">{error}</span>
                  </div>
                )}
                {result && (
                  <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-green-700 text-sm">评估已完成，查看结果标签页</span>
                  </div>
                )}
                <Button 
                  onClick={runEvaluation} 
                  disabled={loading}
                  className="w-full"
                  size="lg"
                >
                  {loading ? "评估中..." : "开始评估"}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* 数据输入区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>目标点数据</CardTitle>
                <CardDescription>输入目标轨迹点的JSON数据</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={targetPointsJson}
                  onChange={(e) => setTargetPointsJson(e.target.value)}
                  placeholder="请输入目标点JSON数据..."
                  className="min-h-[300px] font-mono text-sm"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>实际注视点数据</CardTitle>
                <CardDescription>输入实际注视轨迹点的JSON数据</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={actualGazePointsJson}
                  onChange={(e) => setActualGazePointsJson(e.target.value)}
                  placeholder="请输入实际注视点JSON数据..."
                  className="min-h-[300px] font-mono text-sm"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 评估结果标签页 */}
        <TabsContent value="results" className="space-y-6">
          {result && (
            <>
              {/* 总体指标 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">平均误差</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">
                      {result.averageError.toFixed(4)}
                    </div>
                    <p className="text-xs text-muted-foreground">像素</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">最大误差</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-red-600">
                      {result.maxError.toFixed(4)}
                    </div>
                    <p className="text-xs text-muted-foreground">像素</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">跟踪精度</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">
                      {result.trackingAccuracy.toFixed(2)}%
                    </div>
                    <p className="text-xs text-muted-foreground">准确率</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">平滑度评分</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-purple-600">
                      {result.smoothnessScore.toFixed(2)}
                    </div>
                    <p className="text-xs text-muted-foreground">分数</p>
                  </CardContent>
                </Card>
              </div>

              {/* 详细统计 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      误差统计
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">最小误差</span>
                          <span className="font-medium">{result.minError.toFixed(4)} px</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">标准差</span>
                          <span className="font-medium">{result.errorStandardDeviation.toFixed(4)} px</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">RMS误差</span>
                          <span className="font-medium">{result.rmsError.toFixed(4)} px</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">路径偏差</span>
                          <span className="font-medium">{result.pathDeviation.toFixed(4)}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 网格统计 */}
                {result.gridStatistics && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Grid3X3 className="h-5 w-5" />
                        网格分布统计
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">中心区域点数</span>
                            <span className="font-medium">{result.gridStatistics.centerCount}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">中心区域占比</span>
                            <span className="font-medium">{result.gridStatistics.centerPercentage.toFixed(2)}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">边缘区域点数</span>
                            <span className="font-medium">{result.gridStatistics.edgeCount}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">边缘区域占比</span>
                            <span className="font-medium">{result.gridStatistics.edgePercentage.toFixed(2)}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">总点数</span>
                            <span className="font-medium">{result.gridStatistics.totalPoints}</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* 点误差详情 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    点误差详情
                  </CardTitle>
                  <CardDescription>每个点的详细误差信息</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">索引</th>
                          <th className="text-left p-2">目标坐标</th>
                          <th className="text-left p-2">实际坐标</th>
                          <th className="text-left p-2">误差距离</th>
                          <th className="text-left p-2">X误差</th>
                          <th className="text-left p-2">Y误差</th>
                          <th className="text-left p-2">时间戳</th>
                        </tr>
                      </thead>
                      <tbody>
                        {result.pointErrors.map((point, index) => (
                          <tr key={index} className="border-b hover:bg-gray-50">
                            <td className="p-2">{point.index}</td>
                            <td className="p-2">({point.targetX.toFixed(1)}, {point.targetY.toFixed(1)})</td>
                            <td className="p-2">({point.actualX.toFixed(1)}, {point.actualY.toFixed(1)})</td>
                            <td className="p-2">
                              <Badge variant={point.errorDistance > result.averageError ? "destructive" : "default"}>
                                {point.errorDistance.toFixed(4)}
                              </Badge>
                            </td>
                            <td className="p-2 text-center">
                              <span className={point.errorX > 0 ? "text-red-600" : "text-blue-600"}>
                                {point.errorX > 0 ? "+" : ""}{point.errorX.toFixed(2)}
                              </span>
                            </td>
                            <td className="p-2 text-center">
                              <span className={point.errorY > 0 ? "text-red-600" : "text-blue-600"}>
                                {point.errorY > 0 ? "+" : ""}{point.errorY.toFixed(2)}
                              </span>
                            </td>
                            <td className="p-2 text-xs text-gray-500">
                              {new Date(point.timestamp).toLocaleTimeString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        {/* 可视化分析标签页 */}
        <TabsContent value="visualization" className="space-y-6">
          {result && (
            <>
              {/* 轨迹对比可视化 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    轨迹对比可视化
                  </CardTitle>
                  <CardDescription>
                    红色圆圈为目标点，蓝色点为实际注视点，连线显示误差方向
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="w-full h-96 border rounded-lg bg-gray-50 relative overflow-hidden">
                    <svg width="100%" height="100%" viewBox="0 0 800 600" className="absolute inset-0">
                      {/* 目标点 */}
                      {result.pointErrors.map((point, index) => (
                        <g key={`target-${index}`}>
                          <circle
                            cx={point.targetX * 800 / screenWidth}
                            cy={point.targetY * 600 / screenHeight}
                            r="8"
                            fill="none"
                            stroke="red"
                            strokeWidth="2"
                            opacity="0.7"
                          />
                          <text
                            x={point.targetX * 800 / screenWidth + 12}
                            y={point.targetY * 600 / screenHeight + 4}
                            className="text-xs fill-red-600"
                          >
                            T{index}
                          </text>
                        </g>
                      ))}

                      {/* 实际注视点 */}
                      {result.pointErrors.map((point, index) => (
                        <g key={`actual-${index}`}>
                          <circle
                            cx={point.actualX * 800 / screenWidth}
                            cy={point.actualY * 600 / screenHeight}
                            r="4"
                            fill="blue"
                            opacity="0.8"
                          />
                          <text
                            x={point.actualX * 800 / screenWidth + 8}
                            y={point.actualY * 600 / screenHeight - 8}
                            className="text-xs fill-blue-600"
                          >
                            A{index}
                          </text>
                        </g>
                      ))}

                      {/* 误差连线 */}
                      {result.pointErrors.map((point, index) => (
                        <line
                          key={`error-line-${index}`}
                          x1={point.targetX * 800 / screenWidth}
                          y1={point.targetY * 600 / screenHeight}
                          x2={point.actualX * 800 / screenWidth}
                          y2={point.actualY * 600 / screenHeight}
                          stroke="orange"
                          strokeWidth="1"
                          strokeDasharray="3,3"
                          opacity="0.6"
                        />
                      ))}

                      {/* 目标轨迹连线 */}
                      {result.pointErrors.slice(0, -1).map((point, index) => (
                        <line
                          key={`target-path-${index}`}
                          x1={point.targetX * 800 / screenWidth}
                          y1={point.targetY * 600 / screenHeight}
                          x2={result.pointErrors[index + 1].targetX * 800 / screenWidth}
                          y2={result.pointErrors[index + 1].targetY * 600 / screenHeight}
                          stroke="red"
                          strokeWidth="2"
                          opacity="0.3"
                        />
                      ))}

                      {/* 实际轨迹连线 */}
                      {result.pointErrors.slice(0, -1).map((point, index) => (
                        <line
                          key={`actual-path-${index}`}
                          x1={point.actualX * 800 / screenWidth}
                          y1={point.actualY * 600 / screenHeight}
                          x2={result.pointErrors[index + 1].actualX * 800 / screenWidth}
                          y2={result.pointErrors[index + 1].actualY * 600 / screenHeight}
                          stroke="blue"
                          strokeWidth="2"
                          opacity="0.5"
                        />
                      ))}
                    </svg>
                  </div>
                </CardContent>
              </Card>

              {/* 网格分布可视化 */}
              {result.gridStatistics && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Grid3X3 className="h-5 w-5" />
                      网格分布可视化
                    </CardTitle>
                    <CardDescription>
                      9宫格区域分布热力图，颜色深度表示点数密度
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="w-full max-w-md mx-auto">
                      <div className="grid grid-cols-3 gap-1 aspect-square">
                        {result.gridStatistics.gridCounts.map((count, index) => {
                          const percentage = result.gridStatistics.gridPercentages[index]
                          const intensity = Math.min(percentage / 50, 1) // 最大50%为满色
                          const bgColor = count > 0
                            ? `rgba(59, 130, 246, ${0.2 + intensity * 0.8})`
                            : 'rgba(229, 231, 235, 0.5)'

                          return (
                            <div
                              key={index}
                              className="aspect-square border border-gray-300 rounded flex flex-col items-center justify-center text-xs font-medium"
                              style={{ backgroundColor: bgColor }}
                            >
                              <div className="text-lg font-bold">{count}</div>
                              <div className="text-xs text-gray-600">{percentage.toFixed(1)}%</div>
                            </div>
                          )
                        })}
                      </div>
                      <div className="mt-4 text-center text-sm text-gray-600">
                        <div className="flex justify-center items-center gap-4">
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 bg-blue-200 rounded"></div>
                            <span>低密度</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 bg-blue-600 rounded"></div>
                            <span>高密度</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 分析总结 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    分析总结
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-2">误差特征分析</h4>
                      <ul className="space-y-1 text-sm text-gray-600">
                        <li>• 平均误差: {result.averageError.toFixed(4)} 像素</li>
                        <li>• 最大误差: {result.maxError.toFixed(4)} 像素</li>
                        <li>• 跟踪精度: {result.trackingAccuracy.toFixed(2)}%</li>
                        <li>• 平滑度评分: {result.smoothnessScore.toFixed(2)}</li>
                      </ul>
                    </div>
                    {result.gridStatistics && (
                      <div>
                        <h4 className="font-semibold mb-2">视线分布特征</h4>
                        <ul className="space-y-1 text-sm text-gray-600">
                          <li>• 中心区域占比: {result.gridStatistics.centerPercentage.toFixed(2)}%</li>
                          <li>• 边缘区域占比: {result.gridStatistics.edgePercentage.toFixed(2)}%</li>
                          <li>• 最高集中区域: 区域{result.gridStatistics.gridCounts.indexOf(Math.max(...result.gridStatistics.gridCounts))}</li>
                          <li>• 分布均匀度: {result.gridStatistics.gridCounts.filter(c => c > 0).length}/9 区域有分布</li>
                        </ul>
                      </div>
                    )}
                  </div>

                  <div className="pt-4 border-t">
                    <h4 className="font-semibold mb-2">建议与改进</h4>
                    <div className="text-sm text-gray-600 space-y-1">
                      {result.trackingAccuracy >= 95 && (
                        <p className="text-green-600">✓ 跟踪精度优秀，眼动控制能力良好</p>
                      )}
                      {result.trackingAccuracy < 90 && (
                        <p className="text-orange-600">⚠ 跟踪精度偏低，建议进行眼动训练</p>
                      )}
                      {result.smoothnessScore >= 95 && (
                        <p className="text-green-600">✓ 眼动轨迹平滑，运动控制稳定</p>
                      )}
                      {result.smoothnessScore < 90 && (
                        <p className="text-orange-600">⚠ 眼动轨迹不够平滑，可能存在跳跃或抖动</p>
                      )}
                      {result.gridStatistics && result.gridStatistics.centerPercentage < 30 && (
                        <p className="text-blue-600">ℹ 视线偏向边缘区域，符合边缘优先的视觉模式</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
