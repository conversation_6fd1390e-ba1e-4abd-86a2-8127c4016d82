"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/gaze-stability/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @swc/helpers/_/_class_call_check */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_call_check.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./lib/config.ts\");\n\n\n\n\n\nvar ApiService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function ApiService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, ApiService);\n        var baseURL = (0,_config__WEBPACK_IMPORTED_MODULE_0__.getApiBaseURL)();\n        (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('API Service initialized with baseURL:', baseURL);\n        this.instance = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n            baseURL: baseURL,\n            timeout: _config__WEBPACK_IMPORTED_MODULE_0__.config.api.timeout,\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        // 请求拦截器\n        this.instance.interceptors.request.use(function(config) {\n            var _config_method;\n            (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('API Request:', (_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toUpperCase(), config.url, config.data);\n            // 可以在这里添加认证token等\n            var token = localStorage.getItem('authToken');\n            if (token) {\n                config.headers.Authorization = \"Bearer \".concat(token);\n            }\n            return config;\n        }, function(error) {\n            (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('API Request Error:', error);\n            return Promise.reject(error);\n        });\n        // 响应拦截器\n        this.instance.interceptors.response.use(function(response) {\n            (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('API Response:', response.status, response.config.url, response.data);\n            var data = response.data;\n            if (data.code === 200) {\n                return response;\n            } else {\n                // 处理业务错误\n                (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('API Business Error:', data);\n                throw new Error(data.message || '请求失败');\n            }\n        }, function(error) {\n            (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('API Response Error:', error);\n            // 处理HTTP错误\n            if (error.response) {\n                var _error_response = error.response, status = _error_response.status, data = _error_response.data;\n                (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('HTTP Error Response:', {\n                    status: status,\n                    data: data\n                });\n                switch(status){\n                    case 401:\n                        // 未授权，跳转到登录页\n                        if (true) {\n                            window.location.href = '/login';\n                        }\n                        throw new Error('未授权访问');\n                    case 403:\n                        throw new Error('没有权限访问');\n                    case 404:\n                        throw new Error('请求的资源不存在');\n                    case 500:\n                        // 服务器错误，尝试从响应中获取具体错误信息\n                        var errorMessage = (data === null || data === void 0 ? void 0 : data.message) || (data === null || data === void 0 ? void 0 : data.error) || '服务器内部错误';\n                        throw new Error(errorMessage);\n                    default:\n                        // 其他HTTP错误，尝试从响应中获取错误信息\n                        var message = (data === null || data === void 0 ? void 0 : data.message) || (data === null || data === void 0 ? void 0 : data.error) || \"HTTP \".concat(status, \" 错误\");\n                        throw new Error(message);\n                }\n            } else if (error.request) {\n                // 网络错误\n                (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('Network Error:', error.request);\n                throw new Error('网络连接失败，请检查网络设置');\n            } else {\n                // 请求配置错误\n                (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('Request Config Error:', error.message);\n                throw new Error('请求配置错误: ' + error.message);\n            }\n        });\n    }\n    var _proto = ApiService.prototype;\n    // 通用GET请求\n    _proto.get = function get(url, config) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            var response;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            _this.instance.get(url, config)\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        return [\n                            2,\n                            response.data\n                        ];\n                }\n            });\n        })();\n    };\n    // 通用POST请求\n    _proto.post = function post(url, data, config) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            var response;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            _this.instance.post(url, data, config)\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        return [\n                            2,\n                            response.data\n                        ];\n                }\n            });\n        })();\n    };\n    // 通用PUT请求\n    _proto.put = function put(url, data, config) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            var response;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            _this.instance.put(url, data, config)\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        return [\n                            2,\n                            response.data\n                        ];\n                }\n            });\n        })();\n    };\n    // 通用DELETE请求\n    _proto[\"delete\"] = function _delete(url, config) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            var response;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            _this.instance[\"delete\"](url, config)\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        return [\n                            2,\n                            response.data\n                        ];\n                }\n            });\n        })();\n    };\n    // 注视稳定性测试相关API\n    // 获取注视稳定性测试列表（分页）\n    _proto.getGazeStabilityList = function getGazeStabilityList(params) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/gaze-stability/page', params)\n                ];\n            });\n        })();\n    };\n    // 获取注视稳定性测试详情\n    _proto.getGazeStabilityDetail = function getGazeStabilityDetail(recordId) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.get(\"/api/movement/gaze-stability/record/\".concat(recordId))\n                ];\n            });\n        })();\n    };\n    // 创建注视稳定性测试\n    _proto.createGazeStabilityTest = function createGazeStabilityTest(data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/gaze-stability', data)\n                ];\n            });\n        })();\n    };\n    // 更新注视稳定性测试\n    _proto.updateGazeStabilityTest = function updateGazeStabilityTest(id, data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.put(\"/api/movement/gaze-stability/\".concat(id), data)\n                ];\n            });\n        })();\n    };\n    // 删除注视稳定性测试\n    _proto.deleteGazeStabilityTest = function deleteGazeStabilityTest(id) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this[\"delete\"](\"/api/movement/gaze-stability/\".concat(id))\n                ];\n            });\n        })();\n    };\n    // 扫视能力测试相关API\n    // 获取扫视能力测试列表（分页）\n    _proto.getSaccadeAbilityList = function getSaccadeAbilityList(params) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/saccade-ability/page', params)\n                ];\n            });\n        })();\n    };\n    // 获取扫视能力测试详情\n    _proto.getSaccadeAbilityDetail = function getSaccadeAbilityDetail(recordId) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.get(\"/api/movement/saccade-ability/record/\".concat(recordId))\n                ];\n            });\n        })();\n    };\n    // 创建扫视能力测试\n    _proto.createSaccadeAbilityTest = function createSaccadeAbilityTest(data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/saccade-ability', data)\n                ];\n            });\n        })();\n    };\n    // 更新扫视能力测试\n    _proto.updateSaccadeAbilityTest = function updateSaccadeAbilityTest(id, data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.put(\"/api/movement/saccade-ability/\".concat(id), data)\n                ];\n            });\n        })();\n    };\n    // 删除扫视能力测试\n    _proto.deleteSaccadeAbilityTest = function deleteSaccadeAbilityTest(id) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this[\"delete\"](\"/api/movement/saccade-ability/\".concat(id))\n                ];\n            });\n        })();\n    };\n    // 追随能力测试相关API\n    // 获取追随能力测试列表（分页）\n    _proto.getPursuitAbilityList = function getPursuitAbilityList(params) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/follow-ability/page', params)\n                ];\n            });\n        })();\n    };\n    // 获取追随能力测试详情\n    _proto.getPursuitAbilityDetail = function getPursuitAbilityDetail(recordId) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.get(\"/api/movement/follow-ability/record/\".concat(recordId))\n                ];\n            });\n        })();\n    };\n    // 创建追随能力测试\n    _proto.createPursuitAbilityTest = function createPursuitAbilityTest(data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/follow-ability', data)\n                ];\n            });\n        })();\n    };\n    // 更新追随能力测试\n    _proto.updatePursuitAbilityTest = function updatePursuitAbilityTest(id, data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.put(\"/api/movement/follow-ability/\".concat(id), data)\n                ];\n            });\n        })();\n    };\n    // 删除追随能力测试\n    _proto.deletePursuitAbilityTest = function deletePursuitAbilityTest(id) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this[\"delete\"](\"/api/movement/follow-ability/\".concat(id))\n                ];\n            });\n        })();\n    };\n    // 贝塞尔曲线误差评估相关API\n    // 贝塞尔曲线误差评估\n    _proto.evaluateBezierError = function evaluateBezierError(data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/error-evaluation/bezier', data, {\n                        headers: {\n                            'Content-Type': 'application/json',\n                            'X-Device-Sn': 'TEST_DEVICE_001'\n                        }\n                    })\n                ];\n            });\n        })();\n    };\n    return ApiService;\n}();\n// 创建API服务实例\nvar apiService = new ApiService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});