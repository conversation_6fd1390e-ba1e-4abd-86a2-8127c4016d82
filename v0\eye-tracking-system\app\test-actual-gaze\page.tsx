"use client"

import GazeDistributionGrid from "../../components/gaze-distribution-grid"

export default function TestActualGazePage() {
  // 使用您提供的实际数据格式
  const actualGazePointsData = JSON.stringify([
    {"x": 0.0, "y": 0.0, "distance": 0.0, "duration": 0, "timestamp": 1750662055296},
    {"x": 0.8483347, "y": 0.38725543, "distance": 0.0, "duration": 0, "timestamp": 1750662055296},
    {"x": 0.2, "y": 0.7, "distance": 0.0, "duration": 100, "timestamp": 1750662055396},
    {"x": 0.9, "y": 0.1, "distance": 0.0, "duration": 150, "timestamp": 1750662055496},
    {"x": 0.5, "y": 0.5, "distance": 0.0, "duration": 200, "timestamp": 1750662055596},
    {"x": 0.1, "y": 0.9, "distance": 0.0, "duration": 120, "timestamp": 1750662055696}
  ])

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">实际注视点数据测试</h1>
      <div className="mb-4 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold mb-2">测试数据格式：</h3>
        <pre className="text-xs overflow-auto">
{`[
  {"x": 0.0, "y": 0.0, "distance": 0.0, "duration": 0, "timestamp": 1750662055296},
  {"x": 0.8483347, "y": 0.38725543, "distance": 0.0, "duration": 0, "timestamp": 1750662055296},
  {"x": 0.2, "y": 0.7, "distance": 0.0, "duration": 100, "timestamp": 1750662055396},
  {"x": 0.9, "y": 0.1, "distance": 0.0, "duration": 150, "timestamp": 1750662055496},
  {"x": 0.5, "y": 0.5, "distance": 0.0, "duration": 200, "timestamp": 1750662055596},
  {"x": 0.1, "y": 0.9, "distance": 0.0, "duration": 120, "timestamp": 1750662055696}
]`}
        </pre>
      </div>
      
      <GazeDistributionGrid
        actualGazePoints={actualGazePointsData}
        screenWidth={1920}
        screenHeight={1080}
      />
    </div>
  )
}
