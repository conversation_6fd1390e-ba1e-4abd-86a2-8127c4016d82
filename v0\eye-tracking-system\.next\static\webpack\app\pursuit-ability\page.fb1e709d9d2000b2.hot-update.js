"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pursuit-ability/page",{

/***/ "(app-pages-browser)/./components/pursuit-ability-detail.tsx":
/*!***********************************************!*\
  !*** ./components/pursuit-ability-detail.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PursuitAbilityDetail)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/move.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _bezier_error_visualization__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./bezier-error-visualization */ \"(app-pages-browser)/./components/bezier-error-visualization.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction PursuitAbilityDetail(param) {\n    var recordId = param.recordId, onBack = param.onBack;\n    var _record_pursuitAccuracy, _record_averageVelocity, _record_velocityGain, _record_pursuitAccuracy1, _record_averageVelocity1, _record_velocityGain1, _record_phaseDelay, _record_smoothnessIndex, _record_trackingError, _record_pursuitLatency;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), record = _useState[0], setRecord = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), pursuitPoints = _useState1[0], setPursuitPoints = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), loading = _useState2[0], setLoading = _useState2[1];\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    var toast = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)().toast;\n    // 获取详情数据\n    var fetchDetail = /*#__PURE__*/ function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_11__._)(function() {\n            var response, points, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_12__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        setLoading(true);\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            4,\n                            5\n                        ]);\n                        return [\n                            4,\n                            _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].getPursuitAbilityDetail(recordId)\n                        ];\n                    case 2:\n                        response = _state.sent();\n                        setRecord(response.data);\n                        // 解析追随轨迹数据\n                        if (response.data.gazeTrajectoryJson) {\n                            try {\n                                points = JSON.parse(response.data.gazeTrajectoryJson);\n                                // 确保解析的数据是数组\n                                if (Array.isArray(points)) {\n                                    setPursuitPoints(points);\n                                } else {\n                                    console.error('追随轨迹数据不是数组格式:', points);\n                                    setPursuitPoints([]);\n                                }\n                            } catch (error) {\n                                console.error('解析追随轨迹数据失败:', error);\n                                setPursuitPoints([]);\n                            }\n                        } else {\n                            setPursuitPoints([]);\n                        }\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        toast({\n                            title: \"获取详情失败\",\n                            description: error instanceof Error ? error.message : \"请检查网络连接\",\n                            variant: \"destructive\"\n                        });\n                        console.error('获取追随能力测试详情失败:', error);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchDetail() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PursuitAbilityDetail.useEffect\": function() {\n            if (recordId) {\n                fetchDetail();\n            }\n        }\n    }[\"PursuitAbilityDetail.useEffect\"], [\n        recordId\n    ]);\n    // 状态颜色映射\n    var getStatusColor = function(status) {\n        switch(status){\n            case 'COMPLETED':\n                return 'bg-green-100 text-green-800';\n            case 'PROCESSING':\n                return 'bg-blue-100 text-blue-800';\n            case 'FAILED':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    // 格式化时长\n    var formatDuration = function(seconds) {\n        var minutes = Math.floor(seconds / 60);\n        var remainingSeconds = seconds % 60;\n        return \"\".concat(minutes, \":\").concat(remainingSeconds.toString().padStart(2, '0'));\n    };\n    // 返回处理\n    var handleBack = function() {\n        if (onBack) {\n            onBack();\n        } else {\n            router.back();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2\",\n                    children: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, this);\n    }\n    if (!record) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"未找到测试记录\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    onClick: handleBack,\n                    className: \"mt-4\",\n                    children: \"返回列表\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"返回列表\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"追随能力测试详情\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: [\n                                            \"测试序号: \",\n                                            record.testSequence\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                        className: getStatusColor(record.status),\n                        children: record.statusDesc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-base flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"患者信息\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"姓名\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.patientName\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"住院号\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.inpatientNum\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"病历号\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.caseCardNum\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-base flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"测试信息\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"测试日期\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: new Date(record.testDate).toLocaleDateString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"测试时间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: new Date(record.testDate).toLocaleTimeString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"测试时长\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: formatDuration(record.duration)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-base flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"设备信息\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"设备名称\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.deviceName || '未知设备'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"设备序列号\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-xs\",\n                                                children: record.deviceSn\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"操作员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.operatorName || '未知'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-base flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"测试结果\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"追随精度\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-green-600\",\n                                                children: [\n                                                    (_record_pursuitAccuracy = record.pursuitAccuracy) === null || _record_pursuitAccuracy === void 0 ? void 0 : _record_pursuitAccuracy.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"平均速度\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    (_record_averageVelocity = record.averageVelocity) === null || _record_averageVelocity === void 0 ? void 0 : _record_averageVelocity.toFixed(1),\n                                                    \" \\xb0/s\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"速度增益\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: (_record_velocityGain = record.velocityGain) === null || _record_velocityGain === void 0 ? void 0 : _record_velocityGain.toFixed(2)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                defaultValue: \"overview\",\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"overview\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"测试概览\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"trajectory\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"追随轨迹\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"settings\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"测试配置\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"notes\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"备注信息\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"overview\",\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-base flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"追随统计\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"总追随时间\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: formatDuration(record.totalPursuitTime)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"有效追随时间\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-green-600\",\n                                                                children: formatDuration(record.effectivePursuitTime)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"追随精度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_pursuitAccuracy1 = record.pursuitAccuracy) === null || _record_pursuitAccuracy1 === void 0 ? void 0 : _record_pursuitAccuracy1.toFixed(1),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-base flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"w-4 h-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"速度分析\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"平均速度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_averageVelocity1 = record.averageVelocity) === null || _record_averageVelocity1 === void 0 ? void 0 : _record_averageVelocity1.toFixed(1),\n                                                                    \" \\xb0/s\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"速度增益\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: (_record_velocityGain1 = record.velocityGain) === null || _record_velocityGain1 === void 0 ? void 0 : _record_velocityGain1.toFixed(2)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"相位延迟\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_phaseDelay = record.phaseDelay) === null || _record_phaseDelay === void 0 ? void 0 : _record_phaseDelay.toFixed(1),\n                                                                    \" ms\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-base flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"w-4 h-4 text-purple-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"质量指标\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"平滑度指数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: (_record_smoothnessIndex = record.smoothnessIndex) === null || _record_smoothnessIndex === void 0 ? void 0 : _record_smoothnessIndex.toFixed(2)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"跟踪误差\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_trackingError = record.trackingError) === null || _record_trackingError === void 0 ? void 0 : _record_trackingError.toFixed(2),\n                                                                    \" px\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"追随延迟\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_pursuitLatency = record.pursuitLatency) === null || _record_pursuitLatency === void 0 ? void 0 : _record_pursuitLatency.toFixed(1),\n                                                                    \" ms\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this),\n                            record.followPathPoints && record.actualGazePoints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_bezier_error_visualization__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                followPathPoints: record.followPathPoints,\n                                actualGazePoints: record.actualGazePoints,\n                                screenWidth: 1920,\n                                screenHeight: 1080\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"trajectory\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"追随轨迹可视化\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"显示眼球追随目标的运动轨迹和质量分析\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: record.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: record.imageUrl,\n                                                        alt: \"追随轨迹图\",\n                                                        className: \"w-full h-auto\",\n                                                        onError: function(e) {\n                                                            var _e_currentTarget_nextElementSibling;\n                                                            e.currentTarget.style.display = 'none';\n                                                            (_e_currentTarget_nextElementSibling = e.currentTarget.nextElementSibling) === null || _e_currentTarget_nextElementSibling === void 0 ? void 0 : _e_currentTarget_nextElementSibling.classList.remove('hidden');\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"hidden p-8 text-center text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"w-12 h-12 mx-auto mb-2 text-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"轨迹图像加载失败\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, this),\n                                            pursuitPoints.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                className: \"pb-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                    className: \"text-base\",\n                                                                    children: \"轨迹统计\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"轨迹点数\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: pursuitPoints.length\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"平均速度\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    (pursuitPoints.reduce(function(sum, p) {\n                                                                                        return sum + p.velocity;\n                                                                                    }, 0) / pursuitPoints.length).toFixed(1),\n                                                                                    \" \\xb0/s\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 405,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"平均误差\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    (pursuitPoints.reduce(function(sum, p) {\n                                                                                        return sum + p.trackingError;\n                                                                                    }, 0) / pursuitPoints.length).toFixed(2),\n                                                                                    \" px\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 411,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                className: \"pb-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                    className: \"text-base\",\n                                                                    children: \"质量分析\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"目标命中率\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 424,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-green-600\",\n                                                                                children: [\n                                                                                    (pursuitPoints.filter(function(p) {\n                                                                                        return p.isOnTarget;\n                                                                                    }).length / pursuitPoints.length * 100).toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"平均增益\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 430,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: (pursuitPoints.reduce(function(sum, p) {\n                                                                                    return sum + p.velocityGain;\n                                                                                }, 0) / pursuitPoints.length).toFixed(2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 431,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"平均延迟\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 436,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    (pursuitPoints.reduce(function(sum, p) {\n                                                                                        return sum + p.phaseDelay;\n                                                                                    }, 0) / pursuitPoints.length).toFixed(1),\n                                                                                    \" ms\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 437,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-2 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"暂无轨迹图像\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"settings\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"校准参数\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-sm bg-gray-50 p-3 rounded border overflow-auto\",\n                                                children: record.calibrationParams || '无校准参数'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"环境信息\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-sm bg-gray-50 p-3 rounded border overflow-auto\",\n                                                children: record.environmentInfo || '无环境信息'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"notes\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"测试备注\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded border min-h-32\",\n                                        children: record.notes ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm whitespace-pre-wrap\",\n                                            children: record.notes\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"无备注信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s(PursuitAbilityDetail, \"XkreKmfYdf5DHCF8EMyOAbJ7Agw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = PursuitAbilityDetail;\nvar _c;\n$RefreshReg$(_c, \"PursuitAbilityDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pursuit-ability-detail.tsx\n"));

/***/ })

});