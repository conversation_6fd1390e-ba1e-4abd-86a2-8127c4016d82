/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/grid-demo/page";
exports.ids = ["app/grid-demo/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4c5ceb8252e9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxyZXNlYXJjaC11aVxcdjBcXGV5ZS10cmFja2luZy1zeXN0ZW1cXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0YzVjZWI4MjUyZTlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/grid-demo/page.tsx":
/*!********************************!*\
  !*** ./app/grid-demo/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\app\\\\grid-demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\research-ui\\v0\\eye-tracking-system\\app\\grid-demo\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_auth_wrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/auth-wrapper */ \"(rsc)/./components/auth-wrapper.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"眼球运动评估系统\",\n    description: \"专业的眼球运动分析和评估平台\",\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_wrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFNTUE7QUFIZ0I7QUFDOEI7QUFJN0MsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNYQyxXQUFXO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXWCwySkFBZTtzQkFDOUIsNEVBQUNDLGdFQUFXQTswQkFBRU07Ozs7Ozs7Ozs7Ozs7Ozs7QUFJdEIiLCJzb3VyY2VzIjpbIkQ6XFxyZXNlYXJjaC11aVxcdjBcXGV5ZS10cmFja2luZy1zeXN0ZW1cXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCJcbmltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIlxuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiXG5pbXBvcnQgQXV0aFdyYXBwZXIgZnJvbSBcIi4uL2NvbXBvbmVudHMvYXV0aC13cmFwcGVyXCJcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIuecvOeQg+i/kOWKqOivhOS8sOezu+e7n1wiLFxuICBkZXNjcmlwdGlvbjogXCLkuJPkuJrnmoTnnLznkIPov5DliqjliIbmnpDlkozor4TkvLDlubPlj7BcIixcbiAgICBnZW5lcmF0b3I6ICd2MC5kZXYnXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8QXV0aFdyYXBwZXI+e2NoaWxkcmVufTwvQXV0aFdyYXBwZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJBdXRoV3JhcHBlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImdlbmVyYXRvciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/auth-wrapper.tsx":
/*!*************************************!*\
  !*** ./components/auth-wrapper.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\auth-wrapper.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\research-ui\\v0\\eye-tracking-system\\components\\auth-wrapper.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fgrid-demo%2Fpage&page=%2Fgrid-demo%2Fpage&appPaths=%2Fgrid-demo%2Fpage&pagePath=private-next-app-dir%2Fgrid-demo%2Fpage.tsx&appDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fgrid-demo%2Fpage&page=%2Fgrid-demo%2Fpage&appPaths=%2Fgrid-demo%2Fpage&pagePath=private-next-app-dir%2Fgrid-demo%2Fpage.tsx&appDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/grid-demo/page.tsx */ \"(rsc)/./app/grid-demo/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'grid-demo',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\app\\\\grid-demo\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\app\\\\grid-demo\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/grid-demo/page\",\n        pathname: \"/grid-demo\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fgrid-demo%2Fpage&page=%2Fgrid-demo%2Fpage&appPaths=%2Fgrid-demo%2Fpage&pagePath=private-next-app-dir%2Fgrid-demo%2Fpage.tsx&appDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Capp%5C%5Cgrid-demo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Capp%5C%5Cgrid-demo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/grid-demo/page.tsx */ \"(rsc)/./app/grid-demo/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNyZXNlYXJjaC11aSU1QyU1Q3YwJTVDJTVDZXllLXRyYWNraW5nLXN5c3RlbSU1QyU1Q2FwcCU1QyU1Q2dyaWQtZGVtbyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBdUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHJlc2VhcmNoLXVpXFxcXHYwXFxcXGV5ZS10cmFja2luZy1zeXN0ZW1cXFxcYXBwXFxcXGdyaWQtZGVtb1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Capp%5C%5Cgrid-demo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Ccomponents%5C%5Cauth-wrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Ccomponents%5C%5Cauth-wrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth-wrapper.tsx */ \"(rsc)/./components/auth-wrapper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNyZXNlYXJjaC11aSU1QyU1Q3YwJTVDJTVDZXllLXRyYWNraW5nLXN5c3RlbSU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoLXdyYXBwZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDcmVzZWFyY2gtdWklNUMlNUN2MCU1QyU1Q2V5ZS10cmFja2luZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q3Jlc2VhcmNoLXVpJTVDJTVDdjAlNUMlNUNleWUtdHJhY2tpbmctc3lzdGVtJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUF3SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXHJlc2VhcmNoLXVpXFxcXHYwXFxcXGV5ZS10cmFja2luZy1zeXN0ZW1cXFxcY29tcG9uZW50c1xcXFxhdXRoLXdyYXBwZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Ccomponents%5C%5Cauth-wrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/grid-demo/page.tsx":
/*!********************************!*\
  !*** ./app/grid-demo/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GridDemoPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_grid_demo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/grid-demo */ \"(ssr)/./components/grid-demo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction GridDemoPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_grid_demo__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\app\\\\grid-demo\\\\page.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvZ3JpZC1kZW1vL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRWlEO0FBRWxDLFNBQVNDO0lBQ3RCLHFCQUFPLDhEQUFDRCw2REFBUUE7Ozs7O0FBQ2xCIiwic291cmNlcyI6WyJEOlxccmVzZWFyY2gtdWlcXHYwXFxleWUtdHJhY2tpbmctc3lzdGVtXFxhcHBcXGdyaWQtZGVtb1xccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IEdyaWREZW1vIGZyb20gXCIuLi8uLi9jb21wb25lbnRzL2dyaWQtZGVtb1wiXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEdyaWREZW1vUGFnZSgpIHtcbiAgcmV0dXJuIDxHcmlkRGVtbyAvPlxufVxuIl0sIm5hbWVzIjpbIkdyaWREZW1vIiwiR3JpZERlbW9QYWdlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/grid-demo/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/auth-wrapper.tsx":
/*!*************************************!*\
  !*** ./components/auth-wrapper.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AuthWrapper({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthWrapper.useEffect\": ()=>{\n            const checkAuth = {\n                \"AuthWrapper.useEffect.checkAuth\": ()=>{\n                    try {\n                        const userData = localStorage.getItem(\"eyeTrackingUser\");\n                        if (userData) {\n                            const parsedUser = JSON.parse(userData);\n                            setUser(parsedUser);\n                        } else if (pathname !== \"/login\") {\n                            router.push(\"/login\");\n                        }\n                    } catch (error) {\n                        console.error(\"Auth check failed:\", error);\n                        if (pathname !== \"/login\") {\n                            router.push(\"/login\");\n                        }\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthWrapper.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthWrapper.useEffect\"], [\n        pathname,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\auth-wrapper.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"正在验证身份...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\auth-wrapper.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\auth-wrapper.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\auth-wrapper.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this);\n    }\n    if (pathname === \"/login\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/auth-wrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./components/gaze-distribution-grid.tsx":
/*!***********************************************!*\
  !*** ./components/gaze-distribution-grid.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GazeDistributionGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Eye_Grid3X3_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Eye,Grid3X3!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Eye_Grid3X3_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Eye,Grid3X3!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Eye_Grid3X3_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Eye,Grid3X3!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction GazeDistributionGrid({ actualGazePoints, screenWidth = 1920, screenHeight = 1080 }) {\n    // 解析注视点数据\n    const gazePoints = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"GazeDistributionGrid.useMemo[gazePoints]\": ()=>{\n            try {\n                const parsed = JSON.parse(actualGazePoints);\n                if (Array.isArray(parsed)) {\n                    return parsed.map({\n                        \"GazeDistributionGrid.useMemo[gazePoints]\": (point)=>({\n                                x: typeof point.x === 'number' ? point.x : 0,\n                                y: typeof point.y === 'number' ? point.y : 0,\n                                distance: point.distance || 0,\n                                duration: point.duration || 0,\n                                timestamp: point.timestamp || 0\n                            })\n                    }[\"GazeDistributionGrid.useMemo[gazePoints]\"]);\n                }\n                return [];\n            } catch (error) {\n                console.error('解析注视点数据失败:', error);\n                return [];\n            }\n        }\n    }[\"GazeDistributionGrid.useMemo[gazePoints]\"], [\n        actualGazePoints\n    ]);\n    // 计算九宫格分布\n    const gridDistribution = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"GazeDistributionGrid.useMemo[gridDistribution]\": ()=>{\n            if (gazePoints.length === 0) {\n                return {\n                    gridCounts: Array(9).fill(0),\n                    gridPercentages: Array(9).fill(0),\n                    centerCount: 0,\n                    centerPercentage: 0,\n                    edgeCount: 0,\n                    edgePercentage: 0,\n                    totalPoints: 0\n                };\n            }\n            const gridCounts = Array(9).fill(0);\n            gazePoints.forEach({\n                \"GazeDistributionGrid.useMemo[gridDistribution]\": (point)=>{\n                    // 将归一化坐标转换为网格索引\n                    const gridX = Math.floor(point.x * 3);\n                    const gridY = Math.floor(point.y * 3);\n                    // 确保索引在有效范围内\n                    const clampedX = Math.max(0, Math.min(2, gridX));\n                    const clampedY = Math.max(0, Math.min(2, gridY));\n                    const gridIndex = clampedY * 3 + clampedX;\n                    gridCounts[gridIndex]++;\n                }\n            }[\"GazeDistributionGrid.useMemo[gridDistribution]\"]);\n            const totalPoints = gazePoints.length;\n            const gridPercentages = gridCounts.map({\n                \"GazeDistributionGrid.useMemo[gridDistribution].gridPercentages\": (count)=>count / totalPoints * 100\n            }[\"GazeDistributionGrid.useMemo[gridDistribution].gridPercentages\"]);\n            // 计算中心区域（索引4）和边缘区域的统计\n            const centerCount = gridCounts[4] // 中心格子\n            ;\n            const centerPercentage = centerCount / totalPoints * 100;\n            const edgeCount = totalPoints - centerCount;\n            const edgePercentage = edgeCount / totalPoints * 100;\n            return {\n                gridCounts,\n                gridPercentages,\n                centerCount,\n                centerPercentage,\n                edgeCount,\n                edgePercentage,\n                totalPoints\n            };\n        }\n    }[\"GazeDistributionGrid.useMemo[gridDistribution]\"], [\n        gazePoints\n    ]);\n    // 获取网格区域名称\n    const getGridName = (index)=>{\n        const names = [\n            '左上',\n            '上中',\n            '右上',\n            '左中',\n            '中心',\n            '右中',\n            '左下',\n            '下中',\n            '右下'\n        ];\n        return names[index];\n    };\n    if (gazePoints.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Eye_Grid3X3_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this),\n                            \"注视分布分析\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Eye_Grid3X3_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600\",\n                                children: \"暂无注视点数据\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Eye_Grid3X3_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            \"注视分布分析\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                        children: \"九宫格区域分布热力图，颜色深度表示注视点密度\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-1 aspect-square\",\n                                    children: gridDistribution.gridCounts.map((count, index)=>{\n                                        const percentage = gridDistribution.gridPercentages[index];\n                                        const intensity = Math.min(percentage / 30, 1) // 最大30%为满色\n                                        ;\n                                        const bgColor = count > 0 ? `rgba(59, 130, 246, ${0.2 + intensity * 0.8})` : 'rgba(229, 231, 235, 0.3)';\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-square border border-gray-300 rounded flex flex-col items-center justify-center text-xs font-medium relative group cursor-pointer\",\n                                            style: {\n                                                backgroundColor: bgColor\n                                            },\n                                            title: `${getGridName(index)}: ${count}个点 (${percentage.toFixed(1)}%)`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: count\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: [\n                                                        percentage.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10\",\n                                                    children: [\n                                                        getGridName(index),\n                                                        \"区域\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 text-center text-sm text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-blue-200 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"低密度\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-blue-600 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"高密度\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-3 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: gridDistribution.totalPoints\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: \"总注视点数\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-3 bg-green-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: [\n                                            gridDistribution.centerPercentage.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: \"中心区域占比\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-3 bg-orange-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-600\",\n                                        children: [\n                                            gridDistribution.edgePercentage.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: \"边缘区域占比\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Eye_Grid3X3_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"分布特征\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-1 text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• 最高集中区域: \",\n                                                    getGridName(gridDistribution.gridCounts.indexOf(Math.max(...gridDistribution.gridCounts)))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• 分布均匀度: \",\n                                                    gridDistribution.gridCounts.filter((c)=>c > 0).length,\n                                                    \"/9 区域有分布\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• 中心偏好: \",\n                                                    gridDistribution.centerPercentage > 50 ? '是' : '否'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"• 边缘偏好: \",\n                                                    gridDistribution.edgePercentage > 50 ? '是' : '否'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"视线模式分析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-1 text-gray-600\",\n                                        children: [\n                                            gridDistribution.centerPercentage > 60 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"text-green-600\",\n                                                children: \"✓ 中心集中型 - 注意力高度集中\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            gridDistribution.edgePercentage > 70 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"text-blue-600\",\n                                                children: \"ℹ 边缘扫描型 - 符合边缘优先模式\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            gridDistribution.gridCounts.filter((c)=>c > 0).length >= 7 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"text-purple-600\",\n                                                children: \"◆ 均匀分布型 - 全面扫描模式\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            Math.max(...gridDistribution.gridPercentages) > 40 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"text-orange-600\",\n                                                children: \"⚠ 局部偏重 - 某区域过度关注\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-distribution-grid.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/gaze-distribution-grid.tsx\n");

/***/ }),

/***/ "(ssr)/./components/grid-demo.tsx":
/*!**********************************!*\
  !*** ./components/grid-demo.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GridDemo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Grid3X3_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Grid3X3,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Grid3X3_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Grid3X3,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Grid3X3_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Grid3X3,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _gaze_distribution_grid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./gaze-distribution-grid */ \"(ssr)/./components/gaze-distribution-grid.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction GridDemo() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [currentExample, setCurrentExample] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // 不同的示例数据集\n    const examples = [\n        {\n            name: \"中心集中型\",\n            description: \"注意力主要集中在屏幕中心区域\",\n            data: JSON.stringify([\n                {\n                    \"x\": 0.5,\n                    \"y\": 0.5,\n                    \"distance\": 0.0,\n                    \"duration\": 200,\n                    \"timestamp\": 1750662055296\n                },\n                {\n                    \"x\": 0.45,\n                    \"y\": 0.55,\n                    \"distance\": 0.0,\n                    \"duration\": 150,\n                    \"timestamp\": 1750662055396\n                },\n                {\n                    \"x\": 0.55,\n                    \"y\": 0.45,\n                    \"distance\": 0.0,\n                    \"duration\": 180,\n                    \"timestamp\": 1750662055496\n                },\n                {\n                    \"x\": 0.48,\n                    \"y\": 0.52,\n                    \"distance\": 0.0,\n                    \"duration\": 160,\n                    \"timestamp\": 1750662055596\n                },\n                {\n                    \"x\": 0.52,\n                    \"y\": 0.48,\n                    \"distance\": 0.0,\n                    \"duration\": 170,\n                    \"timestamp\": 1750662055696\n                },\n                {\n                    \"x\": 0.47,\n                    \"y\": 0.53,\n                    \"distance\": 0.0,\n                    \"duration\": 140,\n                    \"timestamp\": 1750662055796\n                },\n                {\n                    \"x\": 0.53,\n                    \"y\": 0.47,\n                    \"distance\": 0.0,\n                    \"duration\": 190,\n                    \"timestamp\": 1750662055896\n                },\n                {\n                    \"x\": 0.49,\n                    \"y\": 0.51,\n                    \"distance\": 0.0,\n                    \"duration\": 165,\n                    \"timestamp\": 1750662055996\n                }\n            ])\n        },\n        {\n            name: \"边缘扫描型\",\n            description: \"视线主要分布在屏幕边缘区域\",\n            data: JSON.stringify([\n                {\n                    \"x\": 0.1,\n                    \"y\": 0.1,\n                    \"distance\": 0.0,\n                    \"duration\": 100,\n                    \"timestamp\": 1750662055296\n                },\n                {\n                    \"x\": 0.9,\n                    \"y\": 0.1,\n                    \"distance\": 0.0,\n                    \"duration\": 120,\n                    \"timestamp\": 1750662055396\n                },\n                {\n                    \"x\": 0.9,\n                    \"y\": 0.9,\n                    \"distance\": 0.0,\n                    \"duration\": 110,\n                    \"timestamp\": 1750662055496\n                },\n                {\n                    \"x\": 0.1,\n                    \"y\": 0.9,\n                    \"distance\": 0.0,\n                    \"duration\": 130,\n                    \"timestamp\": 1750662055596\n                },\n                {\n                    \"x\": 0.5,\n                    \"y\": 0.1,\n                    \"distance\": 0.0,\n                    \"duration\": 90,\n                    \"timestamp\": 1750662055696\n                },\n                {\n                    \"x\": 0.9,\n                    \"y\": 0.5,\n                    \"distance\": 0.0,\n                    \"duration\": 140,\n                    \"timestamp\": 1750662055796\n                },\n                {\n                    \"x\": 0.5,\n                    \"y\": 0.9,\n                    \"distance\": 0.0,\n                    \"duration\": 100,\n                    \"timestamp\": 1750662055896\n                },\n                {\n                    \"x\": 0.1,\n                    \"y\": 0.5,\n                    \"distance\": 0.0,\n                    \"duration\": 115,\n                    \"timestamp\": 1750662055996\n                }\n            ])\n        },\n        {\n            name: \"均匀分布型\",\n            description: \"视线在各个区域相对均匀分布\",\n            data: JSON.stringify([\n                {\n                    \"x\": 0.2,\n                    \"y\": 0.2,\n                    \"distance\": 0.0,\n                    \"duration\": 100,\n                    \"timestamp\": 1750662055296\n                },\n                {\n                    \"x\": 0.5,\n                    \"y\": 0.2,\n                    \"distance\": 0.0,\n                    \"duration\": 110,\n                    \"timestamp\": 1750662055396\n                },\n                {\n                    \"x\": 0.8,\n                    \"y\": 0.2,\n                    \"distance\": 0.0,\n                    \"duration\": 120,\n                    \"timestamp\": 1750662055496\n                },\n                {\n                    \"x\": 0.2,\n                    \"y\": 0.5,\n                    \"distance\": 0.0,\n                    \"duration\": 105,\n                    \"timestamp\": 1750662055596\n                },\n                {\n                    \"x\": 0.5,\n                    \"y\": 0.5,\n                    \"distance\": 0.0,\n                    \"duration\": 115,\n                    \"timestamp\": 1750662055696\n                },\n                {\n                    \"x\": 0.8,\n                    \"y\": 0.5,\n                    \"distance\": 0.0,\n                    \"duration\": 125,\n                    \"timestamp\": 1750662055796\n                },\n                {\n                    \"x\": 0.2,\n                    \"y\": 0.8,\n                    \"distance\": 0.0,\n                    \"duration\": 95,\n                    \"timestamp\": 1750662055896\n                },\n                {\n                    \"x\": 0.5,\n                    \"y\": 0.8,\n                    \"distance\": 0.0,\n                    \"duration\": 135,\n                    \"timestamp\": 1750662055996\n                },\n                {\n                    \"x\": 0.8,\n                    \"y\": 0.8,\n                    \"distance\": 0.0,\n                    \"duration\": 108,\n                    \"timestamp\": 1750662056096\n                }\n            ])\n        },\n        {\n            name: \"左上偏重型\",\n            description: \"注意力主要集中在左上角区域\",\n            data: JSON.stringify([\n                {\n                    \"x\": 0.1,\n                    \"y\": 0.1,\n                    \"distance\": 0.0,\n                    \"duration\": 200,\n                    \"timestamp\": 1750662055296\n                },\n                {\n                    \"x\": 0.15,\n                    \"y\": 0.15,\n                    \"distance\": 0.0,\n                    \"duration\": 180,\n                    \"timestamp\": 1750662055396\n                },\n                {\n                    \"x\": 0.2,\n                    \"y\": 0.1,\n                    \"distance\": 0.0,\n                    \"duration\": 160,\n                    \"timestamp\": 1750662055496\n                },\n                {\n                    \"x\": 0.1,\n                    \"y\": 0.2,\n                    \"distance\": 0.0,\n                    \"duration\": 170,\n                    \"timestamp\": 1750662055596\n                },\n                {\n                    \"x\": 0.25,\n                    \"y\": 0.25,\n                    \"distance\": 0.0,\n                    \"duration\": 150,\n                    \"timestamp\": 1750662055696\n                },\n                {\n                    \"x\": 0.3,\n                    \"y\": 0.1,\n                    \"distance\": 0.0,\n                    \"duration\": 140,\n                    \"timestamp\": 1750662055796\n                },\n                {\n                    \"x\": 0.1,\n                    \"y\": 0.3,\n                    \"distance\": 0.0,\n                    \"duration\": 190,\n                    \"timestamp\": 1750662055896\n                },\n                {\n                    \"x\": 0.6,\n                    \"y\": 0.7,\n                    \"distance\": 0.0,\n                    \"duration\": 80,\n                    \"timestamp\": 1750662055996\n                }\n            ])\n        }\n    ];\n    const currentExampleData = examples[currentExample];\n    const nextExample = ()=>{\n        setCurrentExample((prev)=>(prev + 1) % examples.length);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-6 max-w-6xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid3X3_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"九宫格分布分析演示\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"展示不同视线分布模式的分析结果\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: nextExample,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid3X3_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"切换示例\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>router.push('/'),\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid3X3_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"返回主页\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"当前示例: \",\n                                            currentExampleData.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-normal text-gray-500\",\n                                        children: [\n                                            currentExample + 1,\n                                            \" / \",\n                                            examples.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: currentExampleData.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm\",\n                            children: examples.map((example, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-3 rounded-lg border cursor-pointer transition-colors ${index === currentExample ? 'bg-blue-50 border-blue-200 text-blue-800' : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'}`,\n                                    onClick: ()=>setCurrentExample(index),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-1\",\n                                            children: example.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs\",\n                                            children: example.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_gaze_distribution_grid__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                actualGazePoints: currentExampleData.data,\n                screenWidth: 1920,\n                screenHeight: 1080\n            }, void 0, false, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"功能说明\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-3\",\n                                                children: \"九宫格分析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"热力图显示\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"：颜色深度表示注视点密度\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"数量统计\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"：显示每个区域的注视点数量\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"百分比分析\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"：计算各区域的注视点占比\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"悬停提示\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"：鼠标悬停显示区域详细信息\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-3\",\n                                                children: \"分布特征识别\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"中心集中型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"：中心区域占比 > 60%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"边缘扫描型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"：边缘区域占比 > 70%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"均匀分布型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"：7个以上区域有分布\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"局部偏重\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"：某区域占比 > 40%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-blue-800 mb-2\",\n                                        children: \"数据格式\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-700 mb-2\",\n                                        children: \"组件接受JSON字符串格式的注视点数据，每个点包含以下字段：\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"text-xs bg-white p-3 rounded border overflow-auto\",\n                                        children: `[\n  {\n    \"x\": 0.5,           // X坐标 (0-1归一化)\n    \"y\": 0.5,           // Y坐标 (0-1归一化)\n    \"distance\": 0.0,    // 距离 (可选)\n    \"duration\": 200,    // 持续时间 (可选)\n    \"timestamp\": 1750662055296  // 时间戳 (可选)\n  }\n]`\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\grid-demo.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2dyaWQtZGVtby50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUV1QztBQUN5RDtBQUNqRDtBQUNRO0FBQ1o7QUFDZ0I7QUFFNUMsU0FBU2E7SUFDdEIsTUFBTUMsU0FBU0gsMERBQVNBO0lBQ3hCLE1BQU0sQ0FBQ0ksZ0JBQWdCQyxrQkFBa0IsR0FBR2YsK0NBQVFBLENBQUM7SUFFckQsV0FBVztJQUNYLE1BQU1nQixXQUFXO1FBQ2Y7WUFDRUMsTUFBTTtZQUNOQyxhQUFhO1lBQ2JDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnQkFDbkI7b0JBQUMsS0FBSztvQkFBSyxLQUFLO29CQUFLLFlBQVk7b0JBQUssWUFBWTtvQkFBSyxhQUFhO2dCQUFhO2dCQUNqRjtvQkFBQyxLQUFLO29CQUFNLEtBQUs7b0JBQU0sWUFBWTtvQkFBSyxZQUFZO29CQUFLLGFBQWE7Z0JBQWE7Z0JBQ25GO29CQUFDLEtBQUs7b0JBQU0sS0FBSztvQkFBTSxZQUFZO29CQUFLLFlBQVk7b0JBQUssYUFBYTtnQkFBYTtnQkFDbkY7b0JBQUMsS0FBSztvQkFBTSxLQUFLO29CQUFNLFlBQVk7b0JBQUssWUFBWTtvQkFBSyxhQUFhO2dCQUFhO2dCQUNuRjtvQkFBQyxLQUFLO29CQUFNLEtBQUs7b0JBQU0sWUFBWTtvQkFBSyxZQUFZO29CQUFLLGFBQWE7Z0JBQWE7Z0JBQ25GO29CQUFDLEtBQUs7b0JBQU0sS0FBSztvQkFBTSxZQUFZO29CQUFLLFlBQVk7b0JBQUssYUFBYTtnQkFBYTtnQkFDbkY7b0JBQUMsS0FBSztvQkFBTSxLQUFLO29CQUFNLFlBQVk7b0JBQUssWUFBWTtvQkFBSyxhQUFhO2dCQUFhO2dCQUNuRjtvQkFBQyxLQUFLO29CQUFNLEtBQUs7b0JBQU0sWUFBWTtvQkFBSyxZQUFZO29CQUFLLGFBQWE7Z0JBQWE7YUFDcEY7UUFDSDtRQUNBO1lBQ0VKLE1BQU07WUFDTkMsYUFBYTtZQUNiQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7Z0JBQ25CO29CQUFDLEtBQUs7b0JBQUssS0FBSztvQkFBSyxZQUFZO29CQUFLLFlBQVk7b0JBQUssYUFBYTtnQkFBYTtnQkFDakY7b0JBQUMsS0FBSztvQkFBSyxLQUFLO29CQUFLLFlBQVk7b0JBQUssWUFBWTtvQkFBSyxhQUFhO2dCQUFhO2dCQUNqRjtvQkFBQyxLQUFLO29CQUFLLEtBQUs7b0JBQUssWUFBWTtvQkFBSyxZQUFZO29CQUFLLGFBQWE7Z0JBQWE7Z0JBQ2pGO29CQUFDLEtBQUs7b0JBQUssS0FBSztvQkFBSyxZQUFZO29CQUFLLFlBQVk7b0JBQUssYUFBYTtnQkFBYTtnQkFDakY7b0JBQUMsS0FBSztvQkFBSyxLQUFLO29CQUFLLFlBQVk7b0JBQUssWUFBWTtvQkFBSSxhQUFhO2dCQUFhO2dCQUNoRjtvQkFBQyxLQUFLO29CQUFLLEtBQUs7b0JBQUssWUFBWTtvQkFBSyxZQUFZO29CQUFLLGFBQWE7Z0JBQWE7Z0JBQ2pGO29CQUFDLEtBQUs7b0JBQUssS0FBSztvQkFBSyxZQUFZO29CQUFLLFlBQVk7b0JBQUssYUFBYTtnQkFBYTtnQkFDakY7b0JBQUMsS0FBSztvQkFBSyxLQUFLO29CQUFLLFlBQVk7b0JBQUssWUFBWTtvQkFBSyxhQUFhO2dCQUFhO2FBQ2xGO1FBQ0g7UUFDQTtZQUNFSixNQUFNO1lBQ05DLGFBQWE7WUFDYkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUNuQjtvQkFBQyxLQUFLO29CQUFLLEtBQUs7b0JBQUssWUFBWTtvQkFBSyxZQUFZO29CQUFLLGFBQWE7Z0JBQWE7Z0JBQ2pGO29CQUFDLEtBQUs7b0JBQUssS0FBSztvQkFBSyxZQUFZO29CQUFLLFlBQVk7b0JBQUssYUFBYTtnQkFBYTtnQkFDakY7b0JBQUMsS0FBSztvQkFBSyxLQUFLO29CQUFLLFlBQVk7b0JBQUssWUFBWTtvQkFBSyxhQUFhO2dCQUFhO2dCQUNqRjtvQkFBQyxLQUFLO29CQUFLLEtBQUs7b0JBQUssWUFBWTtvQkFBSyxZQUFZO29CQUFLLGFBQWE7Z0JBQWE7Z0JBQ2pGO29CQUFDLEtBQUs7b0JBQUssS0FBSztvQkFBSyxZQUFZO29CQUFLLFlBQVk7b0JBQUssYUFBYTtnQkFBYTtnQkFDakY7b0JBQUMsS0FBSztvQkFBSyxLQUFLO29CQUFLLFlBQVk7b0JBQUssWUFBWTtvQkFBSyxhQUFhO2dCQUFhO2dCQUNqRjtvQkFBQyxLQUFLO29CQUFLLEtBQUs7b0JBQUssWUFBWTtvQkFBSyxZQUFZO29CQUFJLGFBQWE7Z0JBQWE7Z0JBQ2hGO29CQUFDLEtBQUs7b0JBQUssS0FBSztvQkFBSyxZQUFZO29CQUFLLFlBQVk7b0JBQUssYUFBYTtnQkFBYTtnQkFDakY7b0JBQUMsS0FBSztvQkFBSyxLQUFLO29CQUFLLFlBQVk7b0JBQUssWUFBWTtvQkFBSyxhQUFhO2dCQUFhO2FBQ2xGO1FBQ0g7UUFDQTtZQUNFSixNQUFNO1lBQ05DLGFBQWE7WUFDYkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUNuQjtvQkFBQyxLQUFLO29CQUFLLEtBQUs7b0JBQUssWUFBWTtvQkFBSyxZQUFZO29CQUFLLGFBQWE7Z0JBQWE7Z0JBQ2pGO29CQUFDLEtBQUs7b0JBQU0sS0FBSztvQkFBTSxZQUFZO29CQUFLLFlBQVk7b0JBQUssYUFBYTtnQkFBYTtnQkFDbkY7b0JBQUMsS0FBSztvQkFBSyxLQUFLO29CQUFLLFlBQVk7b0JBQUssWUFBWTtvQkFBSyxhQUFhO2dCQUFhO2dCQUNqRjtvQkFBQyxLQUFLO29CQUFLLEtBQUs7b0JBQUssWUFBWTtvQkFBSyxZQUFZO29CQUFLLGFBQWE7Z0JBQWE7Z0JBQ2pGO29CQUFDLEtBQUs7b0JBQU0sS0FBSztvQkFBTSxZQUFZO29CQUFLLFlBQVk7b0JBQUssYUFBYTtnQkFBYTtnQkFDbkY7b0JBQUMsS0FBSztvQkFBSyxLQUFLO29CQUFLLFlBQVk7b0JBQUssWUFBWTtvQkFBSyxhQUFhO2dCQUFhO2dCQUNqRjtvQkFBQyxLQUFLO29CQUFLLEtBQUs7b0JBQUssWUFBWTtvQkFBSyxZQUFZO29CQUFLLGFBQWE7Z0JBQWE7Z0JBQ2pGO29CQUFDLEtBQUs7b0JBQUssS0FBSztvQkFBSyxZQUFZO29CQUFLLFlBQVk7b0JBQUksYUFBYTtnQkFBYTthQUNqRjtRQUNIO0tBQ0Q7SUFFRCxNQUFNQyxxQkFBcUJOLFFBQVEsQ0FBQ0YsZUFBZTtJQUVuRCxNQUFNUyxjQUFjO1FBQ2xCUixrQkFBa0IsQ0FBQ1MsT0FBUyxDQUFDQSxPQUFPLEtBQUtSLFNBQVNTLE1BQU07SUFDMUQ7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7OzBDQUNDLDhEQUFDRTtnQ0FBR0QsV0FBVTs7a0RBQ1osOERBQUNwQixrR0FBT0E7d0NBQUNvQixXQUFVOzs7Ozs7b0NBQTBCOzs7Ozs7OzBDQUcvQyw4REFBQ0U7Z0NBQUVGLFdBQVU7MENBQXFCOzs7Ozs7Ozs7Ozs7a0NBRXBDLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNyQix5REFBTUE7Z0NBQUN3QixTQUFRO2dDQUFVQyxTQUFTUjtnQ0FBYUksV0FBVTs7a0RBQ3hELDhEQUFDbEIsa0dBQVNBO3dDQUFDa0IsV0FBVTs7Ozs7O29DQUFZOzs7Ozs7OzBDQUduQyw4REFBQ3JCLHlEQUFNQTtnQ0FBQ3dCLFNBQVE7Z0NBQVVDLFNBQVMsSUFBTWxCLE9BQU9tQixJQUFJLENBQUM7Z0NBQU1MLFdBQVU7O2tEQUNuRSw4REFBQ25CLGtHQUFJQTt3Q0FBQ21CLFdBQVU7Ozs7OztvQ0FBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPbEMsOERBQUMxQixxREFBSUE7Z0JBQUMwQixXQUFVOztrQ0FDZCw4REFBQ3ZCLDJEQUFVQTs7MENBQ1QsOERBQUNDLDBEQUFTQTtnQ0FBQ3NCLFdBQVU7O2tEQUNuQiw4REFBQ007OzRDQUFLOzRDQUFPWCxtQkFBbUJMLElBQUk7Ozs7Ozs7a0RBQ3BDLDhEQUFDZ0I7d0NBQUtOLFdBQVU7OzRDQUNiYixpQkFBaUI7NENBQUU7NENBQUlFLFNBQVNTLE1BQU07Ozs7Ozs7Ozs7Ozs7MENBRzNDLDhEQUFDdEIsZ0VBQWVBOzBDQUNibUIsbUJBQW1CSixXQUFXOzs7Ozs7Ozs7Ozs7a0NBR25DLDhEQUFDaEIsNERBQVdBO2tDQUNWLDRFQUFDd0I7NEJBQUlDLFdBQVU7c0NBQ1pYLFNBQVNrQixHQUFHLENBQUMsQ0FBQ0MsU0FBU0Msc0JBQ3RCLDhEQUFDVjtvQ0FFQ0MsV0FBVyxDQUFDLHVEQUF1RCxFQUNqRVMsVUFBVXRCLGlCQUNOLDZDQUNBLDhEQUNKO29DQUNGaUIsU0FBUyxJQUFNaEIsa0JBQWtCcUI7O3NEQUVqQyw4REFBQ0M7NENBQUdWLFdBQVU7c0RBQXNCUSxRQUFRbEIsSUFBSTs7Ozs7O3NEQUNoRCw4REFBQ1k7NENBQUVGLFdBQVU7c0RBQVdRLFFBQVFqQixXQUFXOzs7Ozs7O21DQVR0Q2tCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBaUJmLDhEQUFDekIsK0RBQW9CQTtnQkFDbkIyQixrQkFBa0JoQixtQkFBbUJILElBQUk7Z0JBQ3pDb0IsYUFBYTtnQkFDYkMsY0FBYzs7Ozs7OzBCQUloQiw4REFBQ3ZDLHFEQUFJQTtnQkFBQzBCLFdBQVU7O2tDQUNkLDhEQUFDdkIsMkRBQVVBO2tDQUNULDRFQUFDQywwREFBU0E7c0NBQUM7Ozs7Ozs7Ozs7O2tDQUViLDhEQUFDSCw0REFBV0E7OzBDQUNWLDhEQUFDd0I7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUNXO2dEQUFHVixXQUFVOzBEQUFxQjs7Ozs7OzBEQUNuQyw4REFBQ2M7Z0RBQUdkLFdBQVU7O2tFQUNaLDhEQUFDZTs7NERBQUc7MEVBQUUsOERBQUNDOzBFQUFPOzs7Ozs7NERBQWM7Ozs7Ozs7a0VBQzVCLDhEQUFDRDs7NERBQUc7MEVBQUUsOERBQUNDOzBFQUFPOzs7Ozs7NERBQWE7Ozs7Ozs7a0VBQzNCLDhEQUFDRDs7NERBQUc7MEVBQUUsOERBQUNDOzBFQUFPOzs7Ozs7NERBQWM7Ozs7Ozs7a0VBQzVCLDhEQUFDRDs7NERBQUc7MEVBQUUsOERBQUNDOzBFQUFPOzs7Ozs7NERBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBRy9CLDhEQUFDakI7OzBEQUNDLDhEQUFDVztnREFBR1YsV0FBVTswREFBcUI7Ozs7OzswREFDbkMsOERBQUNjO2dEQUFHZCxXQUFVOztrRUFDWiw4REFBQ2U7OzREQUFHOzBFQUFFLDhEQUFDQzswRUFBTzs7Ozs7OzREQUFjOzs7Ozs7O2tFQUM1Qiw4REFBQ0Q7OzREQUFHOzBFQUFFLDhEQUFDQzswRUFBTzs7Ozs7OzREQUFjOzs7Ozs7O2tFQUM1Qiw4REFBQ0Q7OzREQUFHOzBFQUFFLDhEQUFDQzswRUFBTzs7Ozs7OzREQUFjOzs7Ozs7O2tFQUM1Qiw4REFBQ0Q7OzREQUFHOzBFQUFFLDhEQUFDQzswRUFBTzs7Ozs7OzREQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtqQyw4REFBQ2pCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ1U7d0NBQUdWLFdBQVU7a0RBQW1DOzs7Ozs7a0RBQ2pELDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBNkI7Ozs7OztrREFHMUMsOERBQUNpQjt3Q0FBSWpCLFdBQVU7a0RBQzFCLENBQUM7Ozs7Ozs7O0NBUUQsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT0YiLCJzb3VyY2VzIjpbIkQ6XFxyZXNlYXJjaC11aVxcdjBcXGV5ZS10cmFja2luZy1zeXN0ZW1cXGNvbXBvbmVudHNcXGdyaWQtZGVtby50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIlxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxuaW1wb3J0IHsgR3JpZDNYMywgSG9tZSwgUmVmcmVzaEN3IH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCJcbmltcG9ydCBHYXplRGlzdHJpYnV0aW9uR3JpZCBmcm9tIFwiLi9nYXplLWRpc3RyaWJ1dGlvbi1ncmlkXCJcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gR3JpZERlbW8oKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IFtjdXJyZW50RXhhbXBsZSwgc2V0Q3VycmVudEV4YW1wbGVdID0gdXNlU3RhdGUoMClcblxuICAvLyDkuI3lkIznmoTnpLrkvovmlbDmja7pm4ZcbiAgY29uc3QgZXhhbXBsZXMgPSBbXG4gICAge1xuICAgICAgbmFtZTogXCLkuK3lv4Ppm4bkuK3lnotcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIuazqOaEj+WKm+S4u+imgembhuS4reWcqOWxj+W5leS4reW/g+WMuuWfn1wiLFxuICAgICAgZGF0YTogSlNPTi5zdHJpbmdpZnkoW1xuICAgICAgICB7XCJ4XCI6IDAuNSwgXCJ5XCI6IDAuNSwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMjAwLCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1Mjk2fSxcbiAgICAgICAge1wieFwiOiAwLjQ1LCBcInlcIjogMC41NSwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTUwLCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1Mzk2fSxcbiAgICAgICAge1wieFwiOiAwLjU1LCBcInlcIjogMC40NSwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTgwLCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1NDk2fSxcbiAgICAgICAge1wieFwiOiAwLjQ4LCBcInlcIjogMC41MiwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTYwLCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1NTk2fSxcbiAgICAgICAge1wieFwiOiAwLjUyLCBcInlcIjogMC40OCwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTcwLCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1Njk2fSxcbiAgICAgICAge1wieFwiOiAwLjQ3LCBcInlcIjogMC41MywgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTQwLCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1Nzk2fSxcbiAgICAgICAge1wieFwiOiAwLjUzLCBcInlcIjogMC40NywgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTkwLCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1ODk2fSxcbiAgICAgICAge1wieFwiOiAwLjQ5LCBcInlcIjogMC41MSwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTY1LCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1OTk2fVxuICAgICAgXSlcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6IFwi6L6557yY5omr5o+P5Z6LXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCLop4bnur/kuLvopoHliIbluIPlnKjlsY/luZXovrnnvJjljLrln59cIixcbiAgICAgIGRhdGE6IEpTT04uc3RyaW5naWZ5KFtcbiAgICAgICAge1wieFwiOiAwLjEsIFwieVwiOiAwLjEsIFwiZGlzdGFuY2VcIjogMC4wLCBcImR1cmF0aW9uXCI6IDEwMCwgXCJ0aW1lc3RhbXBcIjogMTc1MDY2MjA1NTI5Nn0sXG4gICAgICAgIHtcInhcIjogMC45LCBcInlcIjogMC4xLCBcImRpc3RhbmNlXCI6IDAuMCwgXCJkdXJhdGlvblwiOiAxMjAsIFwidGltZXN0YW1wXCI6IDE3NTA2NjIwNTUzOTZ9LFxuICAgICAgICB7XCJ4XCI6IDAuOSwgXCJ5XCI6IDAuOSwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTEwLCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1NDk2fSxcbiAgICAgICAge1wieFwiOiAwLjEsIFwieVwiOiAwLjksIFwiZGlzdGFuY2VcIjogMC4wLCBcImR1cmF0aW9uXCI6IDEzMCwgXCJ0aW1lc3RhbXBcIjogMTc1MDY2MjA1NTU5Nn0sXG4gICAgICAgIHtcInhcIjogMC41LCBcInlcIjogMC4xLCBcImRpc3RhbmNlXCI6IDAuMCwgXCJkdXJhdGlvblwiOiA5MCwgXCJ0aW1lc3RhbXBcIjogMTc1MDY2MjA1NTY5Nn0sXG4gICAgICAgIHtcInhcIjogMC45LCBcInlcIjogMC41LCBcImRpc3RhbmNlXCI6IDAuMCwgXCJkdXJhdGlvblwiOiAxNDAsIFwidGltZXN0YW1wXCI6IDE3NTA2NjIwNTU3OTZ9LFxuICAgICAgICB7XCJ4XCI6IDAuNSwgXCJ5XCI6IDAuOSwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTAwLCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1ODk2fSxcbiAgICAgICAge1wieFwiOiAwLjEsIFwieVwiOiAwLjUsIFwiZGlzdGFuY2VcIjogMC4wLCBcImR1cmF0aW9uXCI6IDExNSwgXCJ0aW1lc3RhbXBcIjogMTc1MDY2MjA1NTk5Nn1cbiAgICAgIF0pXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiBcIuWdh+WMgOWIhuW4g+Wei1wiLFxuICAgICAgZGVzY3JpcHRpb246IFwi6KeG57q/5Zyo5ZCE5Liq5Yy65Z+f55u45a+55Z2H5YyA5YiG5biDXCIsXG4gICAgICBkYXRhOiBKU09OLnN0cmluZ2lmeShbXG4gICAgICAgIHtcInhcIjogMC4yLCBcInlcIjogMC4yLCBcImRpc3RhbmNlXCI6IDAuMCwgXCJkdXJhdGlvblwiOiAxMDAsIFwidGltZXN0YW1wXCI6IDE3NTA2NjIwNTUyOTZ9LFxuICAgICAgICB7XCJ4XCI6IDAuNSwgXCJ5XCI6IDAuMiwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTEwLCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1Mzk2fSxcbiAgICAgICAge1wieFwiOiAwLjgsIFwieVwiOiAwLjIsIFwiZGlzdGFuY2VcIjogMC4wLCBcImR1cmF0aW9uXCI6IDEyMCwgXCJ0aW1lc3RhbXBcIjogMTc1MDY2MjA1NTQ5Nn0sXG4gICAgICAgIHtcInhcIjogMC4yLCBcInlcIjogMC41LCBcImRpc3RhbmNlXCI6IDAuMCwgXCJkdXJhdGlvblwiOiAxMDUsIFwidGltZXN0YW1wXCI6IDE3NTA2NjIwNTU1OTZ9LFxuICAgICAgICB7XCJ4XCI6IDAuNSwgXCJ5XCI6IDAuNSwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTE1LCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1Njk2fSxcbiAgICAgICAge1wieFwiOiAwLjgsIFwieVwiOiAwLjUsIFwiZGlzdGFuY2VcIjogMC4wLCBcImR1cmF0aW9uXCI6IDEyNSwgXCJ0aW1lc3RhbXBcIjogMTc1MDY2MjA1NTc5Nn0sXG4gICAgICAgIHtcInhcIjogMC4yLCBcInlcIjogMC44LCBcImRpc3RhbmNlXCI6IDAuMCwgXCJkdXJhdGlvblwiOiA5NSwgXCJ0aW1lc3RhbXBcIjogMTc1MDY2MjA1NTg5Nn0sXG4gICAgICAgIHtcInhcIjogMC41LCBcInlcIjogMC44LCBcImRpc3RhbmNlXCI6IDAuMCwgXCJkdXJhdGlvblwiOiAxMzUsIFwidGltZXN0YW1wXCI6IDE3NTA2NjIwNTU5OTZ9LFxuICAgICAgICB7XCJ4XCI6IDAuOCwgXCJ5XCI6IDAuOCwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTA4LCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU2MDk2fVxuICAgICAgXSlcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6IFwi5bem5LiK5YGP6YeN5Z6LXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCLms6jmhI/lipvkuLvopoHpm4bkuK3lnKjlt6bkuIrop5LljLrln59cIixcbiAgICAgIGRhdGE6IEpTT04uc3RyaW5naWZ5KFtcbiAgICAgICAge1wieFwiOiAwLjEsIFwieVwiOiAwLjEsIFwiZGlzdGFuY2VcIjogMC4wLCBcImR1cmF0aW9uXCI6IDIwMCwgXCJ0aW1lc3RhbXBcIjogMTc1MDY2MjA1NTI5Nn0sXG4gICAgICAgIHtcInhcIjogMC4xNSwgXCJ5XCI6IDAuMTUsIFwiZGlzdGFuY2VcIjogMC4wLCBcImR1cmF0aW9uXCI6IDE4MCwgXCJ0aW1lc3RhbXBcIjogMTc1MDY2MjA1NTM5Nn0sXG4gICAgICAgIHtcInhcIjogMC4yLCBcInlcIjogMC4xLCBcImRpc3RhbmNlXCI6IDAuMCwgXCJkdXJhdGlvblwiOiAxNjAsIFwidGltZXN0YW1wXCI6IDE3NTA2NjIwNTU0OTZ9LFxuICAgICAgICB7XCJ4XCI6IDAuMSwgXCJ5XCI6IDAuMiwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTcwLCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1NTk2fSxcbiAgICAgICAge1wieFwiOiAwLjI1LCBcInlcIjogMC4yNSwgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogMTUwLCBcInRpbWVzdGFtcFwiOiAxNzUwNjYyMDU1Njk2fSxcbiAgICAgICAge1wieFwiOiAwLjMsIFwieVwiOiAwLjEsIFwiZGlzdGFuY2VcIjogMC4wLCBcImR1cmF0aW9uXCI6IDE0MCwgXCJ0aW1lc3RhbXBcIjogMTc1MDY2MjA1NTc5Nn0sXG4gICAgICAgIHtcInhcIjogMC4xLCBcInlcIjogMC4zLCBcImRpc3RhbmNlXCI6IDAuMCwgXCJkdXJhdGlvblwiOiAxOTAsIFwidGltZXN0YW1wXCI6IDE3NTA2NjIwNTU4OTZ9LFxuICAgICAgICB7XCJ4XCI6IDAuNiwgXCJ5XCI6IDAuNywgXCJkaXN0YW5jZVwiOiAwLjAsIFwiZHVyYXRpb25cIjogODAsIFwidGltZXN0YW1wXCI6IDE3NTA2NjIwNTU5OTZ9XG4gICAgICBdKVxuICAgIH1cbiAgXVxuXG4gIGNvbnN0IGN1cnJlbnRFeGFtcGxlRGF0YSA9IGV4YW1wbGVzW2N1cnJlbnRFeGFtcGxlXVxuXG4gIGNvbnN0IG5leHRFeGFtcGxlID0gKCkgPT4ge1xuICAgIHNldEN1cnJlbnRFeGFtcGxlKChwcmV2KSA9PiAocHJldiArIDEpICUgZXhhbXBsZXMubGVuZ3RoKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktNiBtYXgtdy02eGxcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICA8R3JpZDNYMyBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAg5Lmd5a6r5qC85YiG5biD5YiG5p6Q5ryU56S6XG4gICAgICAgICAgPC9oMT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG10LTFcIj7lsZXnpLrkuI3lkIzop4bnur/liIbluIPmqKHlvI/nmoTliIbmnpDnu5Pmnpw8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9e25leHRFeGFtcGxlfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgIOWIh+aNouekuuS+i1xuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnLycpfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgPEhvbWUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICDov5Tlm57kuLvpobVcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOW9k+WJjeekuuS+i+ivtOaYjiAqL31cbiAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxzcGFuPuW9k+WJjeekuuS+izoge2N1cnJlbnRFeGFtcGxlRGF0YS5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ub3JtYWwgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICB7Y3VycmVudEV4YW1wbGUgKyAxfSAvIHtleGFtcGxlcy5sZW5ndGh9XG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIHtjdXJyZW50RXhhbXBsZURhdGEuZGVzY3JpcHRpb259XG4gICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtNCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICB7ZXhhbXBsZXMubWFwKChleGFtcGxlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTMgcm91bmRlZC1sZyBib3JkZXIgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgIGluZGV4ID09PSBjdXJyZW50RXhhbXBsZVxuICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTUwIGJvcmRlci1ibHVlLTIwMCB0ZXh0LWJsdWUtODAwJ1xuICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTUwIGJvcmRlci1ncmF5LTIwMCB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwJ1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRFeGFtcGxlKGluZGV4KX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIG1iLTFcIj57ZXhhbXBsZS5uYW1lfTwvaDQ+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14c1wiPntleGFtcGxlLmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgIDwvQ2FyZD5cblxuICAgICAgey8qIOS5neWuq+agvOWIhuW4g+WIhuaekOe7hOS7tiAqL31cbiAgICAgIDxHYXplRGlzdHJpYnV0aW9uR3JpZFxuICAgICAgICBhY3R1YWxHYXplUG9pbnRzPXtjdXJyZW50RXhhbXBsZURhdGEuZGF0YX1cbiAgICAgICAgc2NyZWVuV2lkdGg9ezE5MjB9XG4gICAgICAgIHNjcmVlbkhlaWdodD17MTA4MH1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiDlip/og73or7TmmI4gKi99XG4gICAgICA8Q2FyZCBjbGFzc05hbWU9XCJtdC02XCI+XG4gICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkVGl0bGU+5Yqf6IO96K+05piOPC9DYXJkVGl0bGU+XG4gICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgbWItM1wiPuS5neWuq+agvOWIhuaekDwvaDQ+XG4gICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgPGxpPuKAoiA8c3Ryb25nPueDreWKm+WbvuaYvuekujwvc3Ryb25nPu+8muminOiJsua3seW6puihqOekuuazqOinhueCueWvhuW6pjwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPuKAoiA8c3Ryb25nPuaVsOmHj+e7n+iuoTwvc3Ryb25nPu+8muaYvuekuuavj+S4quWMuuWfn+eahOazqOinhueCueaVsOmHjzwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPuKAoiA8c3Ryb25nPueZvuWIhuavlOWIhuaekDwvc3Ryb25nPu+8muiuoeeul+WQhOWMuuWfn+eahOazqOinhueCueWNoOavlDwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPuKAoiA8c3Ryb25nPuaCrOWBnOaPkOekujwvc3Ryb25nPu+8mum8oOagh+aCrOWBnOaYvuekuuWMuuWfn+ivpue7huS/oeaBrzwvbGk+XG4gICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIG1iLTNcIj7liIbluIPnibnlvoHor4bliKs8L2g0PlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yIHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIDxsaT7igKIgPHN0cm9uZz7kuK3lv4Ppm4bkuK3lnos8L3N0cm9uZz7vvJrkuK3lv4PljLrln5/ljaDmr5QgPiA2MCU8L2xpPlxuICAgICAgICAgICAgICAgIDxsaT7igKIgPHN0cm9uZz7ovrnnvJjmiavmj4/lnos8L3N0cm9uZz7vvJrovrnnvJjljLrln5/ljaDmr5QgPiA3MCU8L2xpPlxuICAgICAgICAgICAgICAgIDxsaT7igKIgPHN0cm9uZz7lnYfljIDliIbluIPlnos8L3N0cm9uZz7vvJo35Liq5Lul5LiK5Yy65Z+f5pyJ5YiG5biDPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+4oCiIDxzdHJvbmc+5bGA6YOo5YGP6YeNPC9zdHJvbmc+77ya5p+Q5Yy65Z+f5Y2g5q+UID4gNDAlPC9saT5cbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBwLTQgYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtYmx1ZS04MDAgbWItMlwiPuaVsOaNruagvOW8jzwvaDQ+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICDnu4Tku7bmjqXlj5dKU09O5a2X56ym5Liy5qC85byP55qE5rOo6KeG54K55pWw5o2u77yM5q+P5Liq54K55YyF5ZCr5Lul5LiL5a2X5q6177yaXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8cHJlIGNsYXNzTmFtZT1cInRleHQteHMgYmctd2hpdGUgcC0zIHJvdW5kZWQgYm9yZGVyIG92ZXJmbG93LWF1dG9cIj5cbntgW1xuICB7XG4gICAgXCJ4XCI6IDAuNSwgICAgICAgICAgIC8vIFjlnZDmoIcgKDAtMeW9kuS4gOWMlilcbiAgICBcInlcIjogMC41LCAgICAgICAgICAgLy8gWeWdkOaghyAoMC0x5b2S5LiA5YyWKVxuICAgIFwiZGlzdGFuY2VcIjogMC4wLCAgICAvLyDot53nprsgKOWPr+mAiSlcbiAgICBcImR1cmF0aW9uXCI6IDIwMCwgICAgLy8g5oyB57ut5pe26Ze0ICjlj6/pgIkpXG4gICAgXCJ0aW1lc3RhbXBcIjogMTc1MDY2MjA1NTI5NiAgLy8g5pe26Ze05oizICjlj6/pgIkpXG4gIH1cbl1gfVxuICAgICAgICAgICAgPC9wcmU+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJ1dHRvbiIsIkdyaWQzWDMiLCJIb21lIiwiUmVmcmVzaEN3IiwidXNlUm91dGVyIiwiR2F6ZURpc3RyaWJ1dGlvbkdyaWQiLCJHcmlkRGVtbyIsInJvdXRlciIsImN1cnJlbnRFeGFtcGxlIiwic2V0Q3VycmVudEV4YW1wbGUiLCJleGFtcGxlcyIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsImRhdGEiLCJKU09OIiwic3RyaW5naWZ5IiwiY3VycmVudEV4YW1wbGVEYXRhIiwibmV4dEV4YW1wbGUiLCJwcmV2IiwibGVuZ3RoIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwidmFyaWFudCIsIm9uQ2xpY2siLCJwdXNoIiwic3BhbiIsIm1hcCIsImV4YW1wbGUiLCJpbmRleCIsImg0IiwiYWN0dWFsR2F6ZVBvaW50cyIsInNjcmVlbldpZHRoIiwic2NyZWVuSGVpZ2h0IiwidWwiLCJsaSIsInN0cm9uZyIsInByZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/grid-demo.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJEOlxccmVzZWFyY2gtdWlcXHYwXFxleWUtdHJhY2tpbmctc3lzdGVtXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Capp%5C%5Cgrid-demo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Capp%5C%5Cgrid-demo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/grid-demo/page.tsx */ \"(ssr)/./app/grid-demo/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNyZXNlYXJjaC11aSU1QyU1Q3YwJTVDJTVDZXllLXRyYWNraW5nLXN5c3RlbSU1QyU1Q2FwcCU1QyU1Q2dyaWQtZGVtbyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBdUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHJlc2VhcmNoLXVpXFxcXHYwXFxcXGV5ZS10cmFja2luZy1zeXN0ZW1cXFxcYXBwXFxcXGdyaWQtZGVtb1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Capp%5C%5Cgrid-demo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Ccomponents%5C%5Cauth-wrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Ccomponents%5C%5Cauth-wrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth-wrapper.tsx */ \"(ssr)/./components/auth-wrapper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNyZXNlYXJjaC11aSU1QyU1Q3YwJTVDJTVDZXllLXRyYWNraW5nLXN5c3RlbSU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoLXdyYXBwZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDcmVzZWFyY2gtdWklNUMlNUN2MCU1QyU1Q2V5ZS10cmFja2luZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q3Jlc2VhcmNoLXVpJTVDJTVDdjAlNUMlNUNleWUtdHJhY2tpbmctc3lzdGVtJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUF3SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXHJlc2VhcmNoLXVpXFxcXHYwXFxcXGV5ZS10cmFja2luZy1zeXN0ZW1cXFxcY29tcG9uZW50c1xcXFxhdXRoLXdyYXBwZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Ccomponents%5C%5Cauth-wrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cresearch-ui%5C%5Cv0%5C%5Ceye-tracking-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fgrid-demo%2Fpage&page=%2Fgrid-demo%2Fpage&appPaths=%2Fgrid-demo%2Fpage&pagePath=private-next-app-dir%2Fgrid-demo%2Fpage.tsx&appDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cresearch-ui%5Cv0%5Ceye-tracking-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();