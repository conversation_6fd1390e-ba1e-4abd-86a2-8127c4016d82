"use client"

import React, { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Grid3X3, Home, RefreshCw } from "lucide-react"
import { useRouter } from "next/navigation"
import GazeDistributionGrid from "./gaze-distribution-grid"

export default function GridDemo() {
  const router = useRouter()
  const [currentExample, setCurrentExample] = useState(0)

  // 不同的示例数据集
  const examples = [
    {
      name: "中心集中型",
      description: "注意力主要集中在屏幕中心区域",
      data: JSON.stringify([
        {"x": 0.5, "y": 0.5, "distance": 0.0, "duration": 200, "timestamp": 1750662055296},
        {"x": 0.45, "y": 0.55, "distance": 0.0, "duration": 150, "timestamp": 1750662055396},
        {"x": 0.55, "y": 0.45, "distance": 0.0, "duration": 180, "timestamp": 1750662055496},
        {"x": 0.48, "y": 0.52, "distance": 0.0, "duration": 160, "timestamp": 1750662055596},
        {"x": 0.52, "y": 0.48, "distance": 0.0, "duration": 170, "timestamp": 1750662055696},
        {"x": 0.47, "y": 0.53, "distance": 0.0, "duration": 140, "timestamp": 1750662055796},
        {"x": 0.53, "y": 0.47, "distance": 0.0, "duration": 190, "timestamp": 1750662055896},
        {"x": 0.49, "y": 0.51, "distance": 0.0, "duration": 165, "timestamp": 1750662055996}
      ])
    },
    {
      name: "边缘扫描型",
      description: "视线主要分布在屏幕边缘区域",
      data: JSON.stringify([
        {"x": 0.1, "y": 0.1, "distance": 0.0, "duration": 100, "timestamp": 1750662055296},
        {"x": 0.9, "y": 0.1, "distance": 0.0, "duration": 120, "timestamp": 1750662055396},
        {"x": 0.9, "y": 0.9, "distance": 0.0, "duration": 110, "timestamp": 1750662055496},
        {"x": 0.1, "y": 0.9, "distance": 0.0, "duration": 130, "timestamp": 1750662055596},
        {"x": 0.5, "y": 0.1, "distance": 0.0, "duration": 90, "timestamp": 1750662055696},
        {"x": 0.9, "y": 0.5, "distance": 0.0, "duration": 140, "timestamp": 1750662055796},
        {"x": 0.5, "y": 0.9, "distance": 0.0, "duration": 100, "timestamp": 1750662055896},
        {"x": 0.1, "y": 0.5, "distance": 0.0, "duration": 115, "timestamp": 1750662055996}
      ])
    },
    {
      name: "均匀分布型",
      description: "视线在各个区域相对均匀分布",
      data: JSON.stringify([
        {"x": 0.2, "y": 0.2, "distance": 0.0, "duration": 100, "timestamp": 1750662055296},
        {"x": 0.5, "y": 0.2, "distance": 0.0, "duration": 110, "timestamp": 1750662055396},
        {"x": 0.8, "y": 0.2, "distance": 0.0, "duration": 120, "timestamp": 1750662055496},
        {"x": 0.2, "y": 0.5, "distance": 0.0, "duration": 105, "timestamp": 1750662055596},
        {"x": 0.5, "y": 0.5, "distance": 0.0, "duration": 115, "timestamp": 1750662055696},
        {"x": 0.8, "y": 0.5, "distance": 0.0, "duration": 125, "timestamp": 1750662055796},
        {"x": 0.2, "y": 0.8, "distance": 0.0, "duration": 95, "timestamp": 1750662055896},
        {"x": 0.5, "y": 0.8, "distance": 0.0, "duration": 135, "timestamp": 1750662055996},
        {"x": 0.8, "y": 0.8, "distance": 0.0, "duration": 108, "timestamp": 1750662056096}
      ])
    },
    {
      name: "左上偏重型",
      description: "注意力主要集中在左上角区域",
      data: JSON.stringify([
        {"x": 0.1, "y": 0.1, "distance": 0.0, "duration": 200, "timestamp": 1750662055296},
        {"x": 0.15, "y": 0.15, "distance": 0.0, "duration": 180, "timestamp": 1750662055396},
        {"x": 0.2, "y": 0.1, "distance": 0.0, "duration": 160, "timestamp": 1750662055496},
        {"x": 0.1, "y": 0.2, "distance": 0.0, "duration": 170, "timestamp": 1750662055596},
        {"x": 0.25, "y": 0.25, "distance": 0.0, "duration": 150, "timestamp": 1750662055696},
        {"x": 0.3, "y": 0.1, "distance": 0.0, "duration": 140, "timestamp": 1750662055796},
        {"x": 0.1, "y": 0.3, "distance": 0.0, "duration": 190, "timestamp": 1750662055896},
        {"x": 0.6, "y": 0.7, "distance": 0.0, "duration": 80, "timestamp": 1750662055996}
      ])
    }
  ]

  const currentExampleData = examples[currentExample]

  const nextExample = () => {
    setCurrentExample((prev) => (prev + 1) % examples.length)
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <Grid3X3 className="h-8 w-8 text-blue-600" />
            九宫格分布分析演示
          </h1>
          <p className="text-gray-600 mt-1">展示不同视线分布模式的分析结果</p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={nextExample} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            切换示例
          </Button>
          <Button variant="outline" onClick={() => router.push('/')} className="flex items-center gap-2">
            <Home className="h-4 w-4" />
            返回主页
          </Button>
        </div>
      </div>

      {/* 当前示例说明 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>当前示例: {currentExampleData.name}</span>
            <span className="text-sm font-normal text-gray-500">
              {currentExample + 1} / {examples.length}
            </span>
          </CardTitle>
          <CardDescription>
            {currentExampleData.description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
            {examples.map((example, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                  index === currentExample
                    ? 'bg-blue-50 border-blue-200 text-blue-800'
                    : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setCurrentExample(index)}
              >
                <h4 className="font-semibold mb-1">{example.name}</h4>
                <p className="text-xs">{example.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 九宫格分布分析组件 */}
      <GazeDistributionGrid
        actualGazePoints={currentExampleData.data}
        screenWidth={1920}
        screenHeight={1080}
      />

      {/* 功能说明 */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>功能说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3">九宫格分析</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• <strong>热力图显示</strong>：颜色深度表示注视点密度</li>
                <li>• <strong>数量统计</strong>：显示每个区域的注视点数量</li>
                <li>• <strong>百分比分析</strong>：计算各区域的注视点占比</li>
                <li>• <strong>悬停提示</strong>：鼠标悬停显示区域详细信息</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">分布特征识别</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• <strong>中心集中型</strong>：中心区域占比 > 60%</li>
                <li>• <strong>边缘扫描型</strong>：边缘区域占比 > 70%</li>
                <li>• <strong>均匀分布型</strong>：7个以上区域有分布</li>
                <li>• <strong>局部偏重</strong>：某区域占比 > 40%</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">数据格式</h4>
            <p className="text-sm text-blue-700 mb-2">
              组件接受JSON字符串格式的注视点数据，每个点包含以下字段：
            </p>
            <pre className="text-xs bg-white p-3 rounded border overflow-auto">
{`[
  {
    "x": 0.5,           // X坐标 (0-1归一化)
    "y": 0.5,           // Y坐标 (0-1归一化)
    "distance": 0.0,    // 距离 (可选)
    "duration": 200,    // 持续时间 (可选)
    "timestamp": 1750662055296  // 时间戳 (可选)
  }
]`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
