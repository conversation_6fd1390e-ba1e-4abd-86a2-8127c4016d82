"use client"

import React, { useState, useEffect } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import PursuitAbilityList from './pursuit-ability-list'
import PursuitAbilityDetail from './pursuit-ability-detail'
import { PursuitAbilityRecord } from '@/lib/api'

export default function PursuitAbilityPage() {
  const [currentView, setCurrentView] = useState<'list' | 'detail'>('list')
  const [selectedRecord, setSelectedRecord] = useState<PursuitAbilityRecord | null>(null)
  const [recordIdFromUrl, setRecordIdFromUrl] = useState<number | null>(null)

  const searchParams = useSearchParams()
  const router = useRouter()

  // 检查URL参数中是否有recordId
  useEffect(() => {
    const recordId = searchParams.get('recordId')
    if (recordId) {
      const id = parseInt(recordId, 10)
      if (!isNaN(id)) {
        setRecordIdFromUrl(id)
        setCurrentView('detail')
      }
    }
  }, [searchParams])

  const handleViewDetail = (record: PursuitAbilityRecord) => {
    setSelectedRecord(record)
    setCurrentView('detail')
    // 更新URL，但不刷新页面
    router.push(`/pursuit-ability?recordId=${record.recordId}`, { scroll: false })
  }

  const handleBackToList = () => {
    setSelectedRecord(null)
    setRecordIdFromUrl(null)
    setCurrentView('list')
    // 清除URL参数
    router.push('/pursuit-ability', { scroll: false })
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {currentView === 'list' ? (
        <PursuitAbilityList onViewDetail={handleViewDetail} />
      ) : (
        // 显示详情页面，优先使用URL中的recordId，其次使用选中的记录
        <PursuitAbilityDetail
          recordId={recordIdFromUrl || selectedRecord?.recordId || 0}
          onBack={handleBackToList}
        />
      )}
    </div>
  )
}
