"use client"

import GazeDistributionGrid from "../../components/gaze-distribution-grid"

export default function TestGridPage() {
  // 测试数据 - 模拟API返回的格式
  const testActualGazePoints = JSON.stringify([
    {"x": 0.1, "y": 0.1, "distance": 0.0, "duration": 100, "timestamp": 1750662055296},
    {"x": 0.8, "y": 0.4, "distance": 0.0, "duration": 150, "timestamp": 1750662055396},
    {"x": 0.2, "y": 0.8, "distance": 0.0, "duration": 120, "timestamp": 1750662055496},
    {"x": 0.5, "y": 0.5, "distance": 0.0, "duration": 200, "timestamp": 1750662055596},
    {"x": 0.9, "y": 0.1, "distance": 0.0, "duration": 80, "timestamp": 1750662055696},
    {"x": 0.1, "y": 0.9, "distance": 0.0, "duration": 110, "timestamp": 1750662055796},
    {"x": 0.7, "y": 0.7, "distance": 0.0, "duration": 90, "timestamp": 1750662055896},
    {"x": 0.3, "y": 0.2, "distance": 0.0, "duration": 130, "timestamp": 1750662055996}
  ])

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">九宫格分布分析测试</h1>
      <GazeDistributionGrid
        actualGazePoints={testActualGazePoints}
        screenWidth={1920}
        screenHeight={1080}
      />
    </div>
  )
}
