# 九宫格注视分布分析功能

## 功能概述

九宫格注视分布分析功能已成功集成到追随能力详情页面中，可以直接使用详情接口返回的 `actualGazePoints` 数据进行视线分布分析。

## 集成位置

### 追随能力详情页面
- **路径**: `/pursuit-ability?recordId={id}`
- **位置**: "测试概览" 标签页
- **数据源**: `record.actualGazePoints` 字段

## 数据格式支持

### 输入数据格式
组件支持以下JSON字符串格式的注视点数据：

```json
[
  {
    "x": 0.0,                    // X坐标 (0-1归一化)
    "y": 0.0,                    // Y坐标 (0-1归一化)
    "distance": 0.0,             // 距离 (可选)
    "duration": 0,               // 持续时间 (可选)
    "timestamp": 1750662055296   // 时间戳 (可选)
  },
  {
    "x": 0.8483347,
    "y": 0.38725543,
    "distance": 0.0,
    "duration": 0,
    "timestamp": 1750662055296
  }
]
```

### 坐标系统
- **坐标范围**: X和Y坐标均为0-1的归一化值
- **坐标映射**: 
  - (0,0) = 屏幕左上角
  - (1,1) = 屏幕右下角
- **网格划分**: 3x3网格，每个格子覆盖1/3的屏幕区域

## 分析功能

### 1. 九宫格热力图
- **可视化显示**: 3x3网格布局
- **颜色编码**: 蓝色深度表示注视点密度
- **数量显示**: 每个格子显示注视点数量和百分比
- **悬停提示**: 鼠标悬停显示区域名称

### 2. 统计指标
- **总注视点数**: 所有有效注视点的总数
- **中心区域占比**: 中心格子(索引4)的注视点占比
- **边缘区域占比**: 除中心外其他8个格子的注视点占比

### 3. 分布特征分析
- **最高集中区域**: 识别注视点最多的区域
- **分布均匀度**: 统计有注视点的区域数量
- **中心偏好**: 判断是否偏好中心区域
- **边缘偏好**: 判断是否偏好边缘区域

### 4. 视线模式识别
- **中心集中型**: 中心区域占比 > 60%
- **边缘扫描型**: 边缘区域占比 > 70%
- **均匀分布型**: 7个以上区域有分布
- **局部偏重**: 某区域占比 > 40%

## 网格区域定义

```
+-------+-------+-------+
| 左上  | 上中  | 右上  |
| (0)   | (1)   | (2)   |
+-------+-------+-------+
| 左中  | 中心  | 右中  |
| (3)   | (4)   | (5)   |
+-------+-------+-------+
| 左下  | 下中  | 右下  |
| (6)   | (7)   | (8)   |
+-------+-------+-------+
```

### 坐标映射规则
- **X轴划分**: [0, 0.33), [0.33, 0.67), [0.67, 1.0]
- **Y轴划分**: [0, 0.33), [0.33, 0.67), [0.67, 1.0]
- **网格索引**: `gridIndex = gridY * 3 + gridX`

## 技术实现

### 组件结构
```typescript
interface GazeDistributionGridProps {
  actualGazePoints: string    // JSON字符串格式的注视点数据
  screenWidth?: number        // 屏幕宽度 (默认1920)
  screenHeight?: number       // 屏幕高度 (默认1080)
}
```

### 数据处理流程
1. **JSON解析**: 解析输入的JSON字符串
2. **数据验证**: 验证坐标数据的有效性
3. **网格映射**: 将归一化坐标映射到网格索引
4. **统计计算**: 计算各区域的点数和百分比
5. **特征分析**: 识别分布模式和视线特征

### 错误处理
- **JSON解析错误**: 显示友好的错误提示
- **空数据处理**: 显示"暂无注视点数据"提示
- **坐标边界**: 自动将超出范围的坐标限制在有效范围内

## 使用方法

### 在详情页面中查看
1. 进入追随能力测试列表
2. 点击任意测试记录查看详情
3. 在"测试概览"标签页中查看九宫格分布分析

### 独立测试
- 访问 `/test-actual-gaze` 页面查看组件测试效果
- 访问 `/grid-demo` 页面查看不同分布模式的演示

## 分析价值

### 临床意义
1. **注意力模式**: 识别患者的视觉注意力分布特征
2. **眼动控制**: 评估眼球运动的控制能力和稳定性
3. **认知功能**: 反映视觉认知和空间感知能力
4. **康复指导**: 为眼动训练提供数据支持

### 应用场景
- **眼动障碍诊断**: 识别异常的视线分布模式
- **康复训练评估**: 监测训练效果和进展
- **认知能力评估**: 评估视觉注意力和空间认知
- **设备校准验证**: 验证眼动设备的准确性

## 技术特点

1. **实时分析**: 自动处理详情接口返回的数据
2. **可视化友好**: 直观的热力图和统计图表
3. **响应式设计**: 适配不同屏幕尺寸
4. **交互体验**: 悬停提示和详细说明
5. **错误容错**: 完善的错误处理和用户提示
6. **性能优化**: 使用useMemo优化计算性能
