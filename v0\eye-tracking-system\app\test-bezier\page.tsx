"use client"

import BezierErrorVisualization from "../../components/bezier-error-visualization"

export default function TestBezierPage() {
  // 测试数据 - 模拟API返回的格式
  const testFollowPathPoints = JSON.stringify([
    [[0.1, 0.1], [0.1, 0.9], [0.2, 0.9], [0.2, 0.1], [0.3, 0.1], [0.3, 0.9], [0.4, 0.9], [0.4, 0.1]]
  ])

  const testActualGazePoints = JSON.stringify([
    [[0.12, 0.08], [0.11, 0.88], [0.19, 0.91], [0.21, 0.09], [0.31, 0.12], [0.29, 0.89], [0.41, 0.88], [0.39, 0.11]]
  ])

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">贝塞尔曲线误差评估测试</h1>
      <BezierErrorVisualization
        followPathPoints={testFollowPathPoints}
        actualGazePoints={testActualGazePoints}
        screenWidth={1920}
        screenHeight={1080}
      />
    </div>
  )
}
