"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pursuit-ability/page",{

/***/ "(app-pages-browser)/./components/bezier-error-visualization.tsx":
/*!***************************************************!*\
  !*** ./components/bezier-error-visualization.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BezierErrorVisualization)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_type_of__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_type_of */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_type_of.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Grid3X3,RefreshCw,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Grid3X3,RefreshCw,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Grid3X3,RefreshCw,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Grid3X3,RefreshCw,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Grid3X3,RefreshCw,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction BezierErrorVisualization(param) {\n    var _this = this;\n    var followPathPoints = param.followPathPoints, actualGazePoints = param.actualGazePoints, _param_screenWidth = param.screenWidth, screenWidth = _param_screenWidth === void 0 ? 1920 : _param_screenWidth, _param_screenHeight = param.screenHeight, screenHeight = _param_screenHeight === void 0 ? 1080 : _param_screenHeight;\n    var _Math;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), loading = _useState[0], setLoading = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), result = _useState1[0], setResult = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), error = _useState2[0], setError = _useState2[1];\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\"), 2), activeTab = _useState3[0], setActiveTab = _useState3[1];\n    var toast = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)().toast;\n    // 解析路径点数据\n    var parsePathPoints = function(jsonString) {\n        try {\n            var parsed = JSON.parse(jsonString);\n            // 处理不同的数据格式\n            if (Array.isArray(parsed)) {\n                // 如果是嵌套数组格式 [[[x1, y1], [x2, y2], ...]]\n                if (parsed.length > 0 && Array.isArray(parsed[0]) && Array.isArray(parsed[0][0])) {\n                    return parsed[0].map(function(point, index) {\n                        return {\n                            x: point[0] * screenWidth,\n                            y: point[1] * screenHeight,\n                            timestamp: Date.now() + index * 100,\n                            isValid: true\n                        };\n                    });\n                } else if (parsed.length > 0 && Array.isArray(parsed[0])) {\n                    return parsed.map(function(point, index) {\n                        return {\n                            x: point[0] * screenWidth,\n                            y: point[1] * screenHeight,\n                            timestamp: Date.now() + index * 100,\n                            isValid: true\n                        };\n                    });\n                } else if (parsed.length > 0 && (0,_swc_helpers_type_of__WEBPACK_IMPORTED_MODULE_8__._)(parsed[0]) === 'object') {\n                    return parsed.map(function(point, index) {\n                        return {\n                            x: point.x || point[0] || 0,\n                            y: point.y || point[1] || 0,\n                            timestamp: point.timestamp || Date.now() + index * 100,\n                            isValid: point.isValid !== undefined ? point.isValid : true\n                        };\n                    });\n                }\n            }\n            return [];\n        } catch (err) {\n            console.error('解析路径点数据失败:', err);\n            return [];\n        }\n    };\n    // 执行贝塞尔曲线误差评估\n    var runEvaluation = /*#__PURE__*/ function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_9__._)(function() {\n            var targetPoints, actualPoints, minLength, alignedTargetPoints, alignedActualPoints, requestData, response, err, errorMessage;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_10__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        setLoading(true);\n                        setError(null);\n                        setResult(null);\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            4,\n                            5\n                        ]);\n                        // 解析目标点和实际注视点\n                        targetPoints = parsePathPoints(followPathPoints);\n                        actualPoints = parsePathPoints(actualGazePoints);\n                        if (targetPoints.length === 0) {\n                            throw new Error('无法解析目标路径点数据');\n                        }\n                        if (actualPoints.length === 0) {\n                            throw new Error('无法解析实际注视点数据');\n                        }\n                        // 确保两个数组长度一致，取较短的长度\n                        minLength = Math.min(targetPoints.length, actualPoints.length);\n                        alignedTargetPoints = targetPoints.slice(0, minLength);\n                        alignedActualPoints = actualPoints.slice(0, minLength);\n                        requestData = {\n                            targetPoints: alignedTargetPoints,\n                            actualGazePoints: alignedActualPoints,\n                            screenWidth: screenWidth,\n                            screenHeight: screenHeight,\n                            enableGridStatistics: true\n                        };\n                        return [\n                            4,\n                            _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].evaluateBezierError(requestData)\n                        ];\n                    case 2:\n                        response = _state.sent();\n                        setResult(response.data);\n                        toast({\n                            title: \"评估完成\",\n                            description: \"贝塞尔曲线误差评估已成功完成\"\n                        });\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        err = _state.sent();\n                        errorMessage = err instanceof Error ? err.message : '评估失败';\n                        setError(errorMessage);\n                        toast({\n                            title: \"评估失败\",\n                            description: errorMessage,\n                            variant: \"destructive\"\n                        });\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function runEvaluation() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    // 组件加载时自动执行评估\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BezierErrorVisualization.useEffect\": function() {\n            if (followPathPoints && actualGazePoints) {\n                runEvaluation();\n            }\n        }\n    }[\"BezierErrorVisualization.useEffect\"], [\n        followPathPoints,\n        actualGazePoints\n    ]);\n    if (!followPathPoints || !actualGazePoints) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            \"贝塞尔曲线误差评估\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600\",\n                                children: \"缺少路径数据，无法进行误差评估\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"贝塞尔曲线误差评估\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"分析目标轨迹与实际注视轨迹的误差和分布特征\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: runEvaluation,\n                            disabled: loading,\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 \".concat(loading ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                loading ? \"评估中...\" : \"重新评估\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"正在进行误差评估...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                        value: activeTab,\n                        onValueChange: setActiveTab,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                className: \"grid w-full grid-cols-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"overview\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"概览\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"visualization\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"轨迹对比\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"grid\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Grid3X3_RefreshCw_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"分布分析\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"overview\",\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-3 bg-blue-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: result.averageError.toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: \"平均误差 (px)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-3 bg-red-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: result.maxError.toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: \"最大误差 (px)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-3 bg-green-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: [\n                                                            result.trackingAccuracy.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: \"跟踪精度\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-3 bg-purple-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-purple-600\",\n                                                        children: result.smoothnessScore.toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: \"平滑度评分\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-sm\",\n                                                        children: \"误差统计\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"最小误差\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            result.minError.toFixed(4),\n                                                                            \" px\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"标准差\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            result.errorStandardDeviation.toFixed(4),\n                                                                            \" px\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"RMS误差\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            result.rmsError.toFixed(4),\n                                                                            \" px\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"路径偏差\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: result.pathDeviation.toFixed(4)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            result.gridStatistics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-sm\",\n                                                        children: \"分布特征\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"中心区域占比\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            result.gridStatistics.centerPercentage.toFixed(1),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"边缘区域占比\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            result.gridStatistics.edgePercentage.toFixed(1),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"总点数\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: result.gridStatistics.totalPoints\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"visualization\",\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-80 border rounded-lg bg-gray-50 relative overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"100%\",\n                                            height: \"100%\",\n                                            viewBox: \"0 0 800 600\",\n                                            className: \"absolute inset-0\",\n                                            children: [\n                                                result.pointErrors.map(function(point, index) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: point.targetX * 800 / screenWidth,\n                                                                cy: point.targetY * 600 / screenHeight,\n                                                                r: \"6\",\n                                                                fill: \"none\",\n                                                                stroke: \"red\",\n                                                                strokeWidth: \"2\",\n                                                                opacity: \"0.7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                x: point.targetX * 800 / screenWidth + 8,\n                                                                y: point.targetY * 600 / screenHeight + 4,\n                                                                className: \"text-xs fill-red-600\",\n                                                                children: [\n                                                                    \"T\",\n                                                                    index\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, \"target-\".concat(index), true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 21\n                                                    }, _this);\n                                                }),\n                                                result.pointErrors.map(function(point, index) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: point.actualX * 800 / screenWidth,\n                                                                cy: point.actualY * 600 / screenHeight,\n                                                                r: \"3\",\n                                                                fill: \"blue\",\n                                                                opacity: \"0.8\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                x: point.actualX * 800 / screenWidth + 6,\n                                                                y: point.actualY * 600 / screenHeight - 6,\n                                                                className: \"text-xs fill-blue-600\",\n                                                                children: [\n                                                                    \"A\",\n                                                                    index\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, \"actual-\".concat(index), true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 21\n                                                    }, _this);\n                                                }),\n                                                result.pointErrors.map(function(point, index) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                        x1: point.targetX * 800 / screenWidth,\n                                                        y1: point.targetY * 600 / screenHeight,\n                                                        x2: point.actualX * 800 / screenWidth,\n                                                        y2: point.actualY * 600 / screenHeight,\n                                                        stroke: \"orange\",\n                                                        strokeWidth: \"1\",\n                                                        strokeDasharray: \"2,2\",\n                                                        opacity: \"0.6\"\n                                                    }, \"error-line-\".concat(index), false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 21\n                                                    }, _this);\n                                                }),\n                                                result.pointErrors.slice(0, -1).map(function(point, index) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                        x1: point.targetX * 800 / screenWidth,\n                                                        y1: point.targetY * 600 / screenHeight,\n                                                        x2: result.pointErrors[index + 1].targetX * 800 / screenWidth,\n                                                        y2: result.pointErrors[index + 1].targetY * 600 / screenHeight,\n                                                        stroke: \"red\",\n                                                        strokeWidth: \"2\",\n                                                        opacity: \"0.4\"\n                                                    }, \"target-path-\".concat(index), false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 21\n                                                    }, _this);\n                                                }),\n                                                result.pointErrors.slice(0, -1).map(function(point, index) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                        x1: point.actualX * 800 / screenWidth,\n                                                        y1: point.actualY * 600 / screenHeight,\n                                                        x2: result.pointErrors[index + 1].actualX * 800 / screenWidth,\n                                                        y2: result.pointErrors[index + 1].actualY * 600 / screenHeight,\n                                                        stroke: \"blue\",\n                                                        strokeWidth: \"2\",\n                                                        opacity: \"0.6\"\n                                                    }, \"actual-path-\".concat(index), false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, _this);\n                                                })\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600 text-center\",\n                                        children: \"红色圆圈：目标点 | 蓝色点：实际注视点 | 橙色虚线：误差方向\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"grid\",\n                                className: \"space-y-4\",\n                                children: result.gridStatistics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-sm mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-3 gap-1 aspect-square\",\n                                                    children: result.gridStatistics.gridCounts.map(function(count, index) {\n                                                        var percentage = result.gridStatistics.gridPercentages[index];\n                                                        var intensity = Math.min(percentage / 50, 1) // 最大50%为满色\n                                                        ;\n                                                        var bgColor = count > 0 ? \"rgba(59, 130, 246, \".concat(0.2 + intensity * 0.8, \")\") : 'rgba(229, 231, 235, 0.5)';\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"aspect-square border border-gray-300 rounded flex flex-col items-center justify-center text-xs font-medium\",\n                                                            style: {\n                                                                backgroundColor: bgColor\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-lg font-bold\",\n                                                                    children: count\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 29\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: [\n                                                                        percentage.toFixed(1),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 29\n                                                                }, _this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 27\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 text-center text-sm text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center items-center gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 bg-blue-200 rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"低密度\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 bg-blue-600 rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"高密度\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold mb-2\",\n                                                            children: \"分布特征\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-1 text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"• 最高集中区域: 区域\",\n                                                                        result.gridStatistics.gridCounts.indexOf((_Math = Math).max.apply(_Math, (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_16__._)(result.gridStatistics.gridCounts)))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"• 分布均匀度: \",\n                                                                        result.gridStatistics.gridCounts.filter(function(c) {\n                                                                            return c > 0;\n                                                                        }).length,\n                                                                        \"/9 区域有分布\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"• 中心偏好: \",\n                                                                        result.gridStatistics.centerPercentage > 50 ? '是' : '否'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"• 边缘偏好: \",\n                                                                        result.gridStatistics.edgePercentage > 50 ? '是' : '否'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold mb-2\",\n                                                            children: \"评估建议\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-1 text-gray-600\",\n                                                            children: [\n                                                                result.trackingAccuracy >= 95 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-green-600\",\n                                                                    children: \"✓ 跟踪精度优秀\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                result.trackingAccuracy < 90 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-orange-600\",\n                                                                    children: \"⚠ 跟踪精度偏低\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                result.smoothnessScore >= 95 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-green-600\",\n                                                                    children: \"✓ 眼动轨迹平滑\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                result.smoothnessScore < 90 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-orange-600\",\n                                                                    children: \"⚠ 轨迹不够平滑\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\bezier-error-visualization.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(BezierErrorVisualization, \"W/eCyGNXi9L/ODFNeU6Txy7yylw=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = BezierErrorVisualization;\nvar _c;\n$RefreshReg$(_c, \"BezierErrorVisualization\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/bezier-error-visualization.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/pursuit-ability-detail.tsx":
/*!***********************************************!*\
  !*** ./components/pursuit-ability-detail.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PursuitAbilityDetail)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/move.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _bezier_error_visualization__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./bezier-error-visualization */ \"(app-pages-browser)/./components/bezier-error-visualization.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction PursuitAbilityDetail(param) {\n    var recordId = param.recordId, onBack = param.onBack;\n    var _record_pursuitAccuracy, _record_averageVelocity, _record_velocityGain, _record_pursuitAccuracy1, _record_averageVelocity1, _record_velocityGain1, _record_phaseDelay, _record_smoothnessIndex, _record_trackingError, _record_pursuitLatency;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), record = _useState[0], setRecord = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), pursuitPoints = _useState1[0], setPursuitPoints = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), loading = _useState2[0], setLoading = _useState2[1];\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    var toast = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)().toast;\n    // 获取详情数据\n    var fetchDetail = /*#__PURE__*/ function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_11__._)(function() {\n            var response, points, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_12__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        setLoading(true);\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            4,\n                            5\n                        ]);\n                        return [\n                            4,\n                            _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].getPursuitAbilityDetail(recordId)\n                        ];\n                    case 2:\n                        response = _state.sent();\n                        setRecord(response.data);\n                        // 解析追随轨迹数据\n                        if (response.data.gazeTrajectoryJson) {\n                            try {\n                                points = JSON.parse(response.data.gazeTrajectoryJson);\n                                // 确保解析的数据是数组\n                                if (Array.isArray(points)) {\n                                    setPursuitPoints(points);\n                                } else {\n                                    console.error('追随轨迹数据不是数组格式:', points);\n                                    setPursuitPoints([]);\n                                }\n                            } catch (error) {\n                                console.error('解析追随轨迹数据失败:', error);\n                                setPursuitPoints([]);\n                            }\n                        } else {\n                            setPursuitPoints([]);\n                        }\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        toast({\n                            title: \"获取详情失败\",\n                            description: error instanceof Error ? error.message : \"请检查网络连接\",\n                            variant: \"destructive\"\n                        });\n                        console.error('获取追随能力测试详情失败:', error);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchDetail() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PursuitAbilityDetail.useEffect\": function() {\n            if (recordId) {\n                fetchDetail();\n            }\n        }\n    }[\"PursuitAbilityDetail.useEffect\"], [\n        recordId\n    ]);\n    // 状态颜色映射\n    var getStatusColor = function(status) {\n        switch(status){\n            case 'COMPLETED':\n                return 'bg-green-100 text-green-800';\n            case 'PROCESSING':\n                return 'bg-blue-100 text-blue-800';\n            case 'FAILED':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    // 格式化时长\n    var formatDuration = function(seconds) {\n        var minutes = Math.floor(seconds / 60);\n        var remainingSeconds = seconds % 60;\n        return \"\".concat(minutes, \":\").concat(remainingSeconds.toString().padStart(2, '0'));\n    };\n    // 返回处理\n    var handleBack = function() {\n        if (onBack) {\n            onBack();\n        } else {\n            router.back();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2\",\n                    children: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this);\n    }\n    if (!record) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"未找到测试记录\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    onClick: handleBack,\n                    className: \"mt-4\",\n                    children: \"返回列表\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"返回列表\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"追随能力测试详情\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: [\n                                            \"测试序号: \",\n                                            record.testSequence\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                        className: getStatusColor(record.status),\n                        children: record.statusDesc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-base flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"患者信息\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"姓名\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.patientName\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"住院号\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.inpatientNum\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"病历号\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.caseCardNum\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-base flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"测试信息\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"测试日期\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: new Date(record.testDate).toLocaleDateString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"测试时间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: new Date(record.testDate).toLocaleTimeString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"测试时长\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: formatDuration(record.duration)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-base flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"设备信息\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"设备名称\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.deviceName || '未知设备'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"设备序列号\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-xs\",\n                                                children: record.deviceSn\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"操作员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.operatorName || '未知'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-base flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"测试结果\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"追随精度\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-green-600\",\n                                                children: [\n                                                    (_record_pursuitAccuracy = record.pursuitAccuracy) === null || _record_pursuitAccuracy === void 0 ? void 0 : _record_pursuitAccuracy.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"平均速度\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    (_record_averageVelocity = record.averageVelocity) === null || _record_averageVelocity === void 0 ? void 0 : _record_averageVelocity.toFixed(1),\n                                                    \" \\xb0/s\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"速度增益\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: (_record_velocityGain = record.velocityGain) === null || _record_velocityGain === void 0 ? void 0 : _record_velocityGain.toFixed(2)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                defaultValue: \"overview\",\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"overview\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"测试概览\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"trajectory\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"追随轨迹\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"settings\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"测试配置\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"notes\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"备注信息\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"overview\",\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-base flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"追随统计\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"总追随时间\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: formatDuration(record.totalPursuitTime)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"有效追随时间\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-green-600\",\n                                                                children: formatDuration(record.effectivePursuitTime)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"追随精度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_pursuitAccuracy1 = record.pursuitAccuracy) === null || _record_pursuitAccuracy1 === void 0 ? void 0 : _record_pursuitAccuracy1.toFixed(1),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-base flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"w-4 h-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"速度分析\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"平均速度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_averageVelocity1 = record.averageVelocity) === null || _record_averageVelocity1 === void 0 ? void 0 : _record_averageVelocity1.toFixed(1),\n                                                                    \" \\xb0/s\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"速度增益\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: (_record_velocityGain1 = record.velocityGain) === null || _record_velocityGain1 === void 0 ? void 0 : _record_velocityGain1.toFixed(2)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"相位延迟\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_phaseDelay = record.phaseDelay) === null || _record_phaseDelay === void 0 ? void 0 : _record_phaseDelay.toFixed(1),\n                                                                    \" ms\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-base flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"w-4 h-4 text-purple-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"质量指标\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"平滑度指数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: (_record_smoothnessIndex = record.smoothnessIndex) === null || _record_smoothnessIndex === void 0 ? void 0 : _record_smoothnessIndex.toFixed(2)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"跟踪误差\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_trackingError = record.trackingError) === null || _record_trackingError === void 0 ? void 0 : _record_trackingError.toFixed(2),\n                                                                    \" px\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"追随延迟\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_pursuitLatency = record.pursuitLatency) === null || _record_pursuitLatency === void 0 ? void 0 : _record_pursuitLatency.toFixed(1),\n                                                                    \" ms\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            record.followPathPoints && record.actualGazePoints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_bezier_error_visualization__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                followPathPoints: record.followPathPoints,\n                                actualGazePoints: record.actualGazePoints,\n                                screenWidth: 1920,\n                                screenHeight: 1080\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"trajectory\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"追随轨迹可视化\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"显示眼球追随目标的运动轨迹和质量分析\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: record.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: record.imageUrl,\n                                                        alt: \"追随轨迹图\",\n                                                        className: \"w-full h-auto\",\n                                                        onError: function(e) {\n                                                            var _e_currentTarget_nextElementSibling;\n                                                            e.currentTarget.style.display = 'none';\n                                                            (_e_currentTarget_nextElementSibling = e.currentTarget.nextElementSibling) === null || _e_currentTarget_nextElementSibling === void 0 ? void 0 : _e_currentTarget_nextElementSibling.classList.remove('hidden');\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"hidden p-8 text-center text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"w-12 h-12 mx-auto mb-2 text-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"轨迹图像加载失败\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, this),\n                                            pursuitPoints.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                className: \"pb-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                    className: \"text-base\",\n                                                                    children: \"轨迹统计\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"轨迹点数\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 402,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: pursuitPoints.length\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 403,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"平均速度\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 406,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    (pursuitPoints.reduce(function(sum, p) {\n                                                                                        return sum + p.velocity;\n                                                                                    }, 0) / pursuitPoints.length).toFixed(1),\n                                                                                    \" \\xb0/s\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 407,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"平均误差\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    (pursuitPoints.reduce(function(sum, p) {\n                                                                                        return sum + p.trackingError;\n                                                                                    }, 0) / pursuitPoints.length).toFixed(2),\n                                                                                    \" px\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 413,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                className: \"pb-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                    className: \"text-base\",\n                                                                    children: \"质量分析\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"目标命中率\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 426,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-green-600\",\n                                                                                children: [\n                                                                                    (pursuitPoints.filter(function(p) {\n                                                                                        return p.isOnTarget;\n                                                                                    }).length / pursuitPoints.length * 100).toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 427,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"平均增益\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: (pursuitPoints.reduce(function(sum, p) {\n                                                                                    return sum + p.velocityGain;\n                                                                                }, 0) / pursuitPoints.length).toFixed(2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"平均延迟\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 438,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    (pursuitPoints.reduce(function(sum, p) {\n                                                                                        return sum + p.phaseDelay;\n                                                                                    }, 0) / pursuitPoints.length).toFixed(1),\n                                                                                    \" ms\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 439,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-2 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"暂无轨迹图像\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"settings\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"校准参数\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-sm bg-gray-50 p-3 rounded border overflow-auto\",\n                                                children: record.calibrationParams || '无校准参数'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"环境信息\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-sm bg-gray-50 p-3 rounded border overflow-auto\",\n                                                children: record.environmentInfo || '无环境信息'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                            lineNumber: 460,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"notes\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"测试备注\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded border min-h-32\",\n                                        children: record.notes ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm whitespace-pre-wrap\",\n                                            children: record.notes\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"无备注信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_s(PursuitAbilityDetail, \"XkreKmfYdf5DHCF8EMyOAbJ7Agw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = PursuitAbilityDetail;\nvar _c;\n$RefreshReg$(_c, \"PursuitAbilityDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pursuit-ability-detail.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-alert.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CircleAlert)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar CircleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CircleAlert\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"8\",\n            y2: \"12\",\n            key: \"1pkeuh\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12.01\",\n            y1: \"16\",\n            y2: \"16\",\n            key: \"4dfq90\"\n        }\n    ]\n]);\n //# sourceMappingURL=circle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/grid-3x3.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Grid3x3)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar Grid3x3 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Grid3x3\", [\n    [\n        \"rect\",\n        {\n            width: \"18\",\n            height: \"18\",\n            x: \"3\",\n            y: \"3\",\n            rx: \"2\",\n            key: \"afitv7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 9h18\",\n            key: \"1pudct\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 15h18\",\n            key: \"5xshup\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 3v18\",\n            key: \"fh3hqa\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M15 3v18\",\n            key: \"14nvp0\"\n        }\n    ]\n]);\n //# sourceMappingURL=grid-3x3.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/refresh-cw.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RefreshCw)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar RefreshCw = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"RefreshCw\", [\n    [\n        \"path\",\n        {\n            d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\",\n            key: \"v9h5vc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 3v5h-5\",\n            key: \"1q7to0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\",\n            key: \"3uifl3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 16H3v5\",\n            key: \"1cv678\"\n        }\n    ]\n]);\n //# sourceMappingURL=refresh-cw.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\n"));

/***/ })

});