    "/api/movement/follow-ability/page": {
      "post": {
        "summary": "分页追随能力评估列表",
        "deprecated": false,
        "description": "",
        "tags": [],
        "parameters": [
          {
            "name": "Content-Type",
            "in": "header",
            "description": "",
            "required": true,
            "example": "application/json",
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "current": {
                    "type": "integer"
                  },
                  "size": {
                    "type": "integer"
                  }
                },
                "required": [
                  "current",
                  "size"
                ]
              },
              "example": {
                "current": 1,
                "size": 10
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    },
                    "data": {
                      "type": "object",
                      "properties": {
                        "current": {
                          "type": "integer"
                        },
                        "size": {
                          "type": "integer"
                        },
                        "total": {
                          "type": "integer"
                        },
                        "pages": {
                          "type": "integer"
                        },
                        "records": {
                          "type": "array",
                          "items": {
                            "type": "object",
                            "properties": {
                              "id": {
                                "type": "integer"
                              },
                              "recordId": {
                                "type": "integer"
                              },
                              "patientId": {
                                "type": "integer"
                              },
                              "patientName": {
                                "type": "string",
                                "nullable": true
                              },
                              "inpatientNum": {
                                "type": "string",
                                "nullable": true
                              },
                              "caseCardNum": {
                                "type": "string",
                                "nullable": true
                              },
                              "deviceId": {
                                "type": "integer"
                              },
                              "deviceName": {
                                "type": "string",
                                "nullable": true
                              },
                              "deviceSn": {
                                "type": "string"
                              },
                              "operatorId": {
                                "type": "integer"
                              },
                              "operatorName": {
                                "type": "string",
                                "nullable": true
                              },
                              "testType": {
                                "type": "string"
                              },
                              "testSequence": {
                                "type": "string"
                              },
                              "testDate": {
                                "type": "string"
                              },
                              "duration": {
                                "type": "integer"
                              },
                              "status": {
                                "type": "string"
                              },
                              "statusDesc": {
                                "type": "string"
                              },
                              "followPathPoints": {
                                "type": "string"
                              },
                              "actualGazePoints": {
                                "type": "string"
                              },
                              "trackingAccuracy": {
                                "type": "number"
                              },
                              "averageError": {
                                "type": "number"
                              },
                              "reactionTime": {
                                "type": "integer"
                              },
                              "smoothnessScore": {
                                "type": "integer"
                              },
                              "completionRate": {
                                "type": "integer"
                              },
                              "bezierCurveData": {
                                "type": "string"
                              },
                              "followCount": {
                                "type": "integer"
                              },
                              "successfulFollows": {
                                "type": "integer"
                              },
                              "pathLength": {
                                "type": "integer"
                              },
                              "actualPathLength": {
                                "type": "number"
                              },
                              "velocityConsistency": {
                                "type": "integer"
                              },
                              "calibrationParams": {
                                "type": "string"
                              },
                              "environmentInfo": {
                                "type": "string"
                              },
                              "notes": {
                                "type": "string"
                              },
                              "imageUrl": {
                                "type": "string",
                                "nullable": true
                              },
                              "createdAt": {
                                "type": "string"
                              },
                              "updatedAt": {
                                "type": "string"
                              }
                            },
                            "required": [
                              "id",
                              "recordId",
                              "patientId",
                              "patientName",
                              "inpatientNum",
                              "caseCardNum",
                              "deviceId",
                              "deviceName",
                              "deviceSn",
                              "operatorId",
                              "operatorName",
                              "testType",
                              "testSequence",
                              "testDate",
                              "duration",
                              "status",
                              "statusDesc",
                              "followPathPoints",
                              "actualGazePoints",
                              "trackingAccuracy",
                              "averageError",
                              "reactionTime",
                              "smoothnessScore",
                              "completionRate",
                              "bezierCurveData",
                              "followCount",
                              "successfulFollows",
                              "pathLength",
                              "actualPathLength",
                              "velocityConsistency",
                              "calibrationParams",
                              "environmentInfo",
                              "notes",
                              "imageUrl",
                              "createdAt",
                              "updatedAt"
                            ]
                          }
                        },
                        "hasPrevious": {
                          "type": "boolean"
                        },
                        "hasNext": {
                          "type": "boolean"
                        }
                      },
                      "required": [
                        "current",
                        "size",
                        "total",
                        "pages",
                        "records",
                        "hasPrevious",
                        "hasNext"
                      ]
                    },
                    "timestamp": {
                      "type": "integer"
                    }
                  },
                  "required": [
                    "code",
                    "message",
                    "data",
                    "timestamp"
                  ]
                },
                "example": {
                  "code": 200,
                  "message": "操作成功",
                  "data": {
                    "current": 1,
                    "size": 10,
                    "total": 9,
                    "pages": 1,
                    "records": [
                      {
                        "id": 9,
                        "recordId": 42,
                        "patientId": 37,
                        "patientName": "韩震",
                        "inpatientNum": "123",
                        "caseCardNum": "123",
                        "deviceId": 12,
                        "deviceName": null,
                        "deviceSn": "DD92443A006",
                        "operatorId": 0,
                        "operatorName": null,
                        "testType": "FOLLOW_ABILITY",
                        "testSequence": "02",
                        "testDate": "2025-06-23 15:00:55",
                        "duration": 60000,
                        "status": "COMPLETED",
                        "statusDesc": "已完成",
                        "followPathPoints": "[[[0.1, 0.1], [0.1, 0.9], [0.2, 0.9], [0.2, 0.1], [0.3, 0.1], [0.3, 0.9], [0.4, 0.9], [0.4, 0.1], [0.5, 0.1], [0.5, 0.9], [0.6, 0.9], [0.6, 0.1], [0.7, 0.1], [0.7, 0.9], [0.8, 0.9], [0.8, 0.1], [0.9, 0.1], [0.9, 0.9]]]",
                        "actualGazePoints": "[{\"x\": 0.0, \"y\": 0.0, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8483347, \"y\": 0.38725543, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8543905, \"y\": 0.32641363, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.97506475, \"y\": 0.061013848, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8847046, \"y\": 0.36617106, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.884881, \"y\": 0.37802693, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.912982, \"y\": 0.5462888, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.913011, \"y\": 0.5838343, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.9135227, \"y\": 0.6235913, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.7317037, \"y\": 0.6869443, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8487195, \"y\": 0.7487929, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.80682737, \"y\": 0.7614798, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.7586285, \"y\": 0.7770674, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.7459054, \"y\": 0.7992808, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.73399234, \"y\": 0.81336784, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.22609985, \"y\": 0.7401909, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.20751214, \"y\": 0.73514766, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5115601, \"y\": 0.7823826, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.52933615, \"y\": 0.81476337, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5125056, \"y\": 0.82361627, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.48738393, \"y\": 0.83061063, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.45756763, \"y\": 0.8251345, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.4086523, \"y\": 0.79066133, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.38842422, \"y\": 0.7851083, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.22713007, \"y\": 0.78394306, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.23464999, \"y\": 0.79819703, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.20235479, \"y\": 0.7986374, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.17868644, \"y\": 0.7966629, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.14995831, \"y\": 0.80483955, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.12202531, \"y\": 0.81163883, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.10456951, \"y\": 0.80669075, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.11697909, \"y\": 0.7729546, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.13885382, \"y\": 0.7358863, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.15465839, \"y\": 0.7281916, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.45917225, \"y\": 0.7253622, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.37577784, \"y\": 0.7297751, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.34290716, \"y\": 0.71836025, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.4336829, \"y\": 0.7078169, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.47324306, \"y\": 0.7016633, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.49224854, \"y\": 0.6814464, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.45907134, \"y\": 0.6929353, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5416047, \"y\": 0.6828013, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5847497, \"y\": 0.67063004, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.64360917, \"y\": 0.6663523, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.6754495, \"y\": 0.67084706, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.68100643, \"y\": 0.69499564, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8656946, \"y\": 0.67046744, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8204622, \"y\": 0.68138087, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8177865, \"y\": 0.6892279, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.83413124, \"y\": 0.6874365, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8475131, \"y\": 0.6730078, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.86592674, \"y\": 0.65428764, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.89283454, \"y\": 0.6474779, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.9075759, \"y\": 0.62912685, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8881234, \"y\": 0.600021, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.868603, \"y\": 0.590073, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.846166, \"y\": 0.58181024, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.50306445, \"y\": 0.48504493, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5706146, \"y\": 0.50735337, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.6263919, \"y\": 0.54126525, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.63897395, \"y\": 0.5544976, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.61579573, \"y\": 0.5629308, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5979222, \"y\": 0.564575, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5675854, \"y\": 0.5604127, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.28743494, \"y\": 0.5689056, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.2579973, \"y\": 0.56357807, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.36545685, \"y\": 0.58123976, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.14308384, \"y\": 0.56344163, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.11541194, \"y\": 0.54317665, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.0684766, \"y\": 0.5518033, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.035150442, \"y\": 0.5690327, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.021710753, \"y\": 0.5695876, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.03588687, \"y\": 0.5478033, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.09464323, \"y\": 0.533491, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.11853363, \"y\": 0.53554535, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.10724276, \"y\": 0.57520115, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.08843226, \"y\": 0.54782385, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.09460305, \"y\": 0.5348169, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.16667233, \"y\": 0.5184916, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.20543745, \"y\": 0.5149985, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.22959566, \"y\": 0.51503, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.25019708, \"y\": 0.51624286, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.41646734, \"y\": 0.48450637, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.4341714, \"y\": 0.47816148, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.41115794, \"y\": 0.48155934, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.4520994, \"y\": 0.4842077, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.47505468, \"y\": 0.4920255, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.48839775, \"y\": 0.52619916, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.5044621, \"y\": 0.50923514, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.65701747, \"y\": 0.49892476, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.67206097, \"y\": 0.5001638, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.657578, \"y\": 0.4966777, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.6845211, \"y\": 0.49830547, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.89408886, \"y\": 0.4696183, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.85353345, \"y\": 0.47134736, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8748417, \"y\": 0.4728609, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8868233, \"y\": 0.46389726, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.48615512, \"y\": 0.38933122, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.7189529, \"y\": 0.39757025, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.47956896, \"y\": 0.3655527, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.45255244, \"y\": 0.37292373, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.4928002, \"y\": 0.37434077, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.52057266, \"y\": 0.38944498, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.36910322, \"y\": 0.41699636, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.334962, \"y\": 0.40841326, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.29694614, \"y\": 0.4066515, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.24690408, \"y\": 0.40720892, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.21785887, \"y\": 0.3995138, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.20036942, \"y\": 0.38725197, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.17426346, \"y\": 0.39120948, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.15599464, \"y\": 0.4045003, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.14599809, \"y\": 0.38831565, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.13268434, \"y\": 0.35573444, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.11555706, \"y\": 0.3514263, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.24082851, \"y\": 0.32727864, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.25990388, \"y\": 0.32019222, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.49199376, \"y\": 0.29187423, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.45299804, \"y\": 0.30380157, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.44211796, \"y\": 0.3142341, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.49875668, \"y\": 0.31633398, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.66218895, \"y\": 0.27706417, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.68256664, \"y\": 0.2684501, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.6945721, \"y\": 0.25823334, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.7489535, \"y\": 0.24927455, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.76578236, \"y\": 0.2261644, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.7617149, \"y\": 0.1987255, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8060589, \"y\": 0.24095818, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8161937, \"y\": 0.2688429, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.82346654, \"y\": 0.28274742, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8679287, \"y\": 0.26264817, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.89556026, \"y\": 0.2427405, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.9183298, \"y\": 0.2204759, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.91316885, \"y\": 0.21064056, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8393088, \"y\": 0.19841076, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.79563785, \"y\": 0.19172916, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.77364326, \"y\": 0.1924692, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.3681615, \"y\": 0.21705037, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.34676033, \"y\": 0.21571153, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.43668598, \"y\": 0.20833772, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.46662042, \"y\": 0.21457236, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.49695435, \"y\": 0.22369747, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.5032785, \"y\": 0.23146442, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.35976943, \"y\": 0.18565644, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.333831, \"y\": 0.1756782, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.3248682, \"y\": 0.16895334, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.34566224, \"y\": 0.20849913, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.35016274, \"y\": 0.22457525, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.3361134, \"y\": 0.2410682, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.2571066, \"y\": 0.23643503, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.2210238, \"y\": 0.23418796, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.19771375, \"y\": 0.22203736, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.17362988, \"y\": 0.21329409, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.15196402, \"y\": 0.1857787, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.12788235, \"y\": 0.20321228, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.18444279, \"y\": 0.17594416, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.20809495, \"y\": 0.16786177, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.2358461, \"y\": 0.16139954, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.2881465, \"y\": 0.13590339, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.29956576, \"y\": 0.14461349, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.49832273, \"y\": 0.08449453, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.5326571, \"y\": 0.070148304, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.5816364, \"y\": 0.046093322, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.6068231, \"y\": 0.03782158, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.62065554, \"y\": 0.030054098, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.82787347, \"y\": 0.07224029, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8463303, \"y\": 0.07954951, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.86511207, \"y\": 0.07796477, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.90700656, \"y\": 0.06730754, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.93003666, \"y\": 0.056729734, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.9457, \"y\": 0.049240407, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.9307888, \"y\": 0.067766234, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.90572786, \"y\": 0.086055405, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.81773126, \"y\": 0.07681636, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.561308, \"y\": 0.029372, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.53830147, \"y\": 0.026875818, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.5633227, \"y\": 0.044779006, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.2516822, \"y\": 0.054630797, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.24634522, \"y\": 0.055892527, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.27286667, \"y\": 0.048236728, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.33323252, \"y\": 0.04194679, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.3578406, \"y\": 0.04289615, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30977547, \"y\": 0.04777413, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.28059608, \"y\": 0.046202008, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.26803103, \"y\": 0.046067074, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.19871709, \"y\": 0.07366523, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.17439587, \"y\": 0.08698571, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.16050133, \"y\": 0.11001137, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.10706836, \"y\": 0.38615358, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.09161748, \"y\": 0.35561633, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.105601594, \"y\": 0.30444926, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.115772165, \"y\": 0.38229334, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.11583937, \"y\": 0.45516875, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.1153247, \"y\": 0.48243308, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.108136274, \"y\": 0.51521146, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.11402277, \"y\": 0.59444743, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.11973018, \"y\": 0.6374733, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.117657386, \"y\": 0.67318106, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.11048015, \"y\": 0.7074717, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.10615428, \"y\": 0.7361834, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.10150453, \"y\": 0.7647793, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.1269197, \"y\": 0.7896668, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.147828, \"y\": 0.80034477, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.16960278, \"y\": 0.7978315, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.19013426, \"y\": 0.751175, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.2118303, \"y\": 0.64612484, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.20224425, \"y\": 0.4355788, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.20932055, \"y\": 0.4091755, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.21357442, \"y\": 0.37998918, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.22056781, \"y\": 0.2932233, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.22383326, \"y\": 0.2526089, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.21959811, \"y\": 0.2206054, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.2160451, \"y\": 0.20260942, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.20515852, \"y\": 0.18466686, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.22302738, \"y\": 0.15793483, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.23364402, \"y\": 0.14212522, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.25480664, \"y\": 0.11682672, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.27174833, \"y\": 0.11841007, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.28811014, \"y\": 0.15808414, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30051234, \"y\": 0.19251572, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.3037628, \"y\": 0.21501178, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30825993, \"y\": 0.28058192, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.31254944, \"y\": 0.3312825, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.3124563, \"y\": 0.36648333, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30614343, \"y\": 0.41056943, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30303073, \"y\": 0.44174758, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30103105, \"y\": 0.4726999, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30112314, \"y\": 0.50668377, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.3132311, \"y\": 0.53600204, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.3061968, \"y\": 0.5763636, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.2950583, \"y\": 0.61507726, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.288805, \"y\": 0.6477458, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.28269458, \"y\": 0.6995622, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.28203452, \"y\": 0.717246, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.29425523, \"y\": 0.7754016, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30182493, \"y\": 0.8055712, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.39371192, \"y\": 0.24128938, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.41541147, \"y\": 0.5115217, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.44739828, \"y\": 0.27084893, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.4516648, \"y\": 0.24074148, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.48546746, \"y\": 0.13581915, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.38615537, \"y\": 0.3006745, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.41432375, \"y\": 0.25413808, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.41962472, \"y\": 0.22517283, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.40963912, \"y\": 0.21095152, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.42173555, \"y\": 0.16572647, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.4332385, \"y\": 0.114691764, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.43733746, \"y\": 0.09558149, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.4334149, \"y\": 0.086339355, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.44967303, \"y\": 0.09403564, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.46812007, \"y\": 0.10675676, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.49423704, \"y\": 0.26456025, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.50692296, \"y\": 0.2898045, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5008607, \"y\": 0.31705588, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.4880091, \"y\": 0.33723927, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5077031, \"y\": 0.39420274, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5178766, \"y\": 0.42129633, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.52197367, \"y\": 0.44375303, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5093094, \"y\": 0.48910555, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.50226176, \"y\": 0.5176759, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.49031305, \"y\": 0.5517758, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.49557367, \"y\": 0.60008854, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.49778983, \"y\": 0.62490666, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.499313, \"y\": 0.65672565, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5042686, \"y\": 0.6717865, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5089628, \"y\": 0.7114653, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5093038, \"y\": 0.74270827, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5120195, \"y\": 0.766132, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.54189575, \"y\": 0.7754384, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.60131, \"y\": 0.22716904, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6100363, \"y\": 0.20535214, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5911251, \"y\": 0.5733552, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.60892427, \"y\": 0.5336046, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.65981054, \"y\": 0.25419274, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6371237, \"y\": 0.24694547, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.635407, \"y\": 0.18928902, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6312753, \"y\": 0.14872691, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.61877126, \"y\": 0.12538277, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6205568, \"y\": 0.10544189, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6312133, \"y\": 0.09160424, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6742814, \"y\": 0.09996704, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.69436765, \"y\": 0.106053546, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.7035687, \"y\": 0.123219416, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6948873, \"y\": 0.1650916, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6907413, \"y\": 0.18684597, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.68949866, \"y\": 0.22408971, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.70242506, \"y\": 0.24898043, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.70557225, \"y\": 0.27212194, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.693953, \"y\": 0.34061176, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6800543, \"y\": 0.3852393, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.67472005, \"y\": 0.4139169, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.66996056, \"y\": 0.442011, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.70584893, \"y\": 0.4936035, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.70908374, \"y\": 0.5165461, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.7126081, \"y\": 0.54515785, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.7306502, \"y\": 0.5379789, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.77344626, \"y\": 0.387164, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.7975075, \"y\": 0.4090653, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.80815196, \"y\": 0.4199717, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.9011145, \"y\": 0.09041342, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.91249067, \"y\": 0.06573297, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.88508016, \"y\": 0.105203494, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.86871123, \"y\": 0.12717341, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.85099, \"y\": 0.14197676, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.83657324, \"y\": 0.15075523, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.85261, \"y\": 0.12370044, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.86607796, \"y\": 0.10322084, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.87989354, \"y\": 0.09779955, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.892967, \"y\": 0.10987817, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.89890426, \"y\": 0.17330757, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.9015206, \"y\": 0.21814781, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.9046446, \"y\": 0.24866273, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.90363777, \"y\": 0.27455235, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.9175477, \"y\": 0.5254818, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.91580456, \"y\": 0.5611804, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.90388983, \"y\": 0.58578724, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.8823229, \"y\": 0.59257156, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.8851623, \"y\": 0.62267846, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}]",
                        "trackingAccuracy": 11.43,
                        "averageError": 0.1963,
                        "reactionTime": 0,
                        "smoothnessScore": 0,
                        "completionRate": 100,
                        "bezierCurveData": "[{\"endPoint\": [0.2, 0.1], \"startPoint\": [0.1, 0.1], \"controlPoint1\": [0.1, 0.9], \"controlPoint2\": [0.2, 0.9]}]",
                        "followCount": 1,
                        "successfulFollows": 0,
                        "pathLength": 8,
                        "actualPathLength": 21.36,
                        "velocityConsistency": 0,
                        "calibrationParams": "\"标准校准\"",
                        "environmentInfo": "\"室内光线充足\"",
                        "notes": "追随能力测试",
                        "imageUrl": "http://localhost:8080/api/files/20250623_150057_4efaa3f7.png",
                        "createdAt": "2025-06-23 15:01:08",
                        "updatedAt": "2025-06-23 15:01:08"
                      },
                      {
                        "id": 8,
                        "recordId": 41,
                        "patientId": 35,
                        "patientName": "gatsbyh",
                        "inpatientNum": "123",
                        "caseCardNum": "123",
                        "deviceId": 12,
                        "deviceName": null,
                        "deviceSn": "DD92443A006",
                        "operatorId": 0,
                        "operatorName": null,
                        "testType": "FOLLOW_ABILITY",
                        "testSequence": "02",
                        "testDate": "2025-06-23 14:34:23",
                        "duration": 60000,
                        "status": "COMPLETED",
                        "statusDesc": "已完成",
                        "followPathPoints": "[[[0.1, 0.1], [0.1, 0.9], [0.2, 0.9], [0.2, 0.1], [0.3, 0.1], [0.3, 0.9], [0.4, 0.9], [0.4, 0.1], [0.5, 0.1], [0.5, 0.9], [0.6, 0.9], [0.6, 0.1], [0.7, 0.1], [0.7, 0.9], [0.8, 0.9], [0.8, 0.1], [0.9, 0.1], [0.9, 0.9]]]",
                        "actualGazePoints": "[{\"x\": 0.92786676, \"y\": 0.7909188, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.79095644, \"y\": 0.2729085, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.7519893, \"y\": 0.3056859, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.7425747, \"y\": 0.32347926, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.71590054, \"y\": 0.36941117, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.9415038, \"y\": 0.2948459, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.8845473, \"y\": 0.41543162, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.87881935, \"y\": 0.44387537, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.894495, \"y\": 0.46723804, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.905094, \"y\": 0.47606966, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.8912438, \"y\": 0.53052855, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.8843584, \"y\": 0.5745101, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.8808975, \"y\": 0.5961738, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.8919297, \"y\": 0.6587326, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.89557946, \"y\": 0.70000577, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.89086276, \"y\": 0.72722054, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.8834714, \"y\": 0.7630616, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.87116367, \"y\": 0.79342, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.85660684, \"y\": 0.80234665, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.79858446, \"y\": 0.8203182, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.7675553, \"y\": 0.81967926, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.7440755, \"y\": 0.82552826, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.30421248, \"y\": 0.8240887, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.2503713, \"y\": 0.8313965, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.15973218, \"y\": 0.83645856, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.12616178, \"y\": 0.84163845, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.49421525, \"y\": 0.81926394, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.22112598, \"y\": 0.8348839, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.19516483, \"y\": 0.83494043, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.2703283, \"y\": 0.83010787, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.31429455, \"y\": 0.82897574, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.33542705, \"y\": 0.8085955, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.2177693, \"y\": 0.8251153, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.19924048, \"y\": 0.8154621, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.18445586, \"y\": 0.80321753, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.15155336, \"y\": 0.8308145, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.13107048, \"y\": 0.80540615, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.12723401, \"y\": 0.77616274, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.168003, \"y\": 0.74572474, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.19497773, \"y\": 0.7339956, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.22780871, \"y\": 0.73811233, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.25324607, \"y\": 0.73342514, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.26957023, \"y\": 0.7213769, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.28442663, \"y\": 0.7109825, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463342}, {\"x\": 0.342457, \"y\": 0.7252651, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.38384706, \"y\": 0.7217063, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.41054648, \"y\": 0.725811, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.76941794, \"y\": 0.7470678, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.7912636, \"y\": 0.72850484, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.62266034, \"y\": 0.74078554, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.59863603, \"y\": 0.73746055, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.579624, \"y\": 0.7289109, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.84356344, \"y\": 0.77154654, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.8591996, \"y\": 0.75841415, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.79968923, \"y\": 0.7365953, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.75809604, \"y\": 0.6999237, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.7918227, \"y\": 0.6851425, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.82046896, \"y\": 0.67106485, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.845542, \"y\": 0.6630113, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.8646128, \"y\": 0.64590317, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.8716381, \"y\": 0.62896436, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.72264653, \"y\": 0.63604474, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.71101606, \"y\": 0.6238769, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.5004191, \"y\": 0.5858856, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.49316388, \"y\": 0.5743529, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.5267891, \"y\": 0.57068694, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.2104885, \"y\": 0.6523414, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.36458725, \"y\": 0.5825199, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.3920334, \"y\": 0.5868395, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.36862472, \"y\": 0.59705997, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.35092625, \"y\": 0.58029294, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.33308655, \"y\": 0.57153696, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.2913954, \"y\": 0.5606328, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.2523296, \"y\": 0.5544615, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.2277319, \"y\": 0.55699646, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.20511068, \"y\": 0.5727547, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.12460706, \"y\": 0.6460195, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.13161264, \"y\": 0.61194277, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.14281148, \"y\": 0.57818186, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.15450245, \"y\": 0.5495245, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.17065781, \"y\": 0.52090156, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.18115547, \"y\": 0.51095533, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.23593384, \"y\": 0.5010648, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.27896327, \"y\": 0.4847721, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.29859626, \"y\": 0.47465858, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.49227217, \"y\": 0.5023955, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.5225779, \"y\": 0.51988494, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.5409506, \"y\": 0.51094717, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.5128937, \"y\": 0.49383304, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.5599198, \"y\": 0.49569294, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.5904771, \"y\": 0.5016956, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.6066346, \"y\": 0.4956979, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.6774659, \"y\": 0.50841665, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.73241824, \"y\": 0.503711, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.76180816, \"y\": 0.50406784, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.7787415, \"y\": 0.51005054, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.7948988, \"y\": 0.49860784, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.8372593, \"y\": 0.48854652, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.8598117, \"y\": 0.4764755, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.8748435, \"y\": 0.46399945, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.8952391, \"y\": 0.44650763, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.48724896, \"y\": 0.35476995, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.4690889, \"y\": 0.35288805, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.7274192, \"y\": 0.36184222, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.74410963, \"y\": 0.3777243, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.72677344, \"y\": 0.39555788, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.5228217, \"y\": 0.38326007, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463343}, {\"x\": 0.2597362, \"y\": 0.40252823, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.50944704, \"y\": 0.40160286, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.46160966, \"y\": 0.38220298, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.43473142, \"y\": 0.3834099, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.40630427, \"y\": 0.3885479, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.20957269, \"y\": 0.41334084, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.24630521, \"y\": 0.39371476, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.18712234, \"y\": 0.38638818, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.1711008, \"y\": 0.39325607, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.14480555, \"y\": 0.414757, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.122381985, \"y\": 0.42899653, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.105431244, \"y\": 0.4363821, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.12331927, \"y\": 0.3865677, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.13989808, \"y\": 0.374809, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.1662542, \"y\": 0.36171797, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.21473044, \"y\": 0.33683172, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.25041753, \"y\": 0.32873097, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.2842739, \"y\": 0.3099955, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.3078969, \"y\": 0.31323463, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.3333067, \"y\": 0.31798846, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.3640659, \"y\": 0.3272434, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.40005344, \"y\": 0.31935304, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.41984746, \"y\": 0.31489694, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.46829957, \"y\": 0.3196643, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.504006, \"y\": 0.3184019, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.5435998, \"y\": 0.31487194, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.55273116, \"y\": 0.31875968, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.5919627, \"y\": 0.31683898, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.6370169, \"y\": 0.3213926, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.65796185, \"y\": 0.324063, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.67230827, \"y\": 0.33329138, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.6910144, \"y\": 0.3279518, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.86162245, \"y\": 0.31518376, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.8865765, \"y\": 0.30334777, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.89988035, \"y\": 0.28910506, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.9117785, \"y\": 0.26286522, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.8854009, \"y\": 0.24426441, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.8479282, \"y\": 0.2243041, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.8291433, \"y\": 0.22444141, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.49995908, \"y\": 0.21556656, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.6436771, \"y\": 0.23094957, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.56604254, \"y\": 0.23080735, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.53287363, \"y\": 0.2304292, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.5111101, \"y\": 0.23874924, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.43211108, \"y\": 0.23097287, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.3975895, \"y\": 0.23287192, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.3563703, \"y\": 0.2474652, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.37909618, \"y\": 0.26368555, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.3291914, \"y\": 0.26528844, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.2982498, \"y\": 0.26069775, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.2801517, \"y\": 0.25747466, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.2168647, \"y\": 0.24587402, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.18972534, \"y\": 0.24574699, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.17261246, \"y\": 0.24301334, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.15499072, \"y\": 0.22860679, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.14895554, \"y\": 0.20877488, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.18126228, \"y\": 0.1934823, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.612313, \"y\": 0.14934534, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.33737764, \"y\": 0.19030827, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.35713574, \"y\": 0.17575909, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463344}, {\"x\": 0.37794685, \"y\": 0.15085714, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.39447016, \"y\": 0.14439099, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.46904874, \"y\": 0.1505547, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.50149035, \"y\": 0.15197241, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.8545339, \"y\": 0.1436185, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.6360278, \"y\": 0.122933865, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.6193226, \"y\": 0.13726407, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.6486533, \"y\": 0.13825743, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.8054613, \"y\": 0.09739607, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.731969, \"y\": 0.1510209, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.7885274, \"y\": 0.15635917, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.82136756, \"y\": 0.14808585, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.84191763, \"y\": 0.14335199, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.86066276, \"y\": 0.13824952, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.91842866, \"y\": 0.122258276, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.9371145, \"y\": 0.11122912, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.91346246, \"y\": 0.09846539, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.47588432, \"y\": 0.055627435, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.43610045, \"y\": 0.055098902, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.36555132, \"y\": 0.05893602, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.3329006, \"y\": 0.07106687, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.30842683, \"y\": 0.07830893, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.24150495, \"y\": 0.060558654, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.21136707, \"y\": 0.055447128, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.4550067, \"y\": 0.03648338, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.42373693, \"y\": 0.048014443, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.39877197, \"y\": 0.056584496, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.3596839, \"y\": 0.068752676, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.3337881, \"y\": 0.079772465, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.31130254, \"y\": 0.08584449, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.2834487, \"y\": 0.09416414, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.24133734, \"y\": 0.09855951, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.18854184, \"y\": 0.11544077, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.15768518, \"y\": 0.12118449, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.14017095, \"y\": 0.13027728, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.13758774, \"y\": 0.16116023, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.13995133, \"y\": 0.20045121, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.13066214, \"y\": 0.2777492, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.11932294, \"y\": 0.31954572, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.11257368, \"y\": 0.34938374, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.1104722, \"y\": 0.38343626, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.10902867, \"y\": 0.40250373, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.10924825, \"y\": 0.43978116, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.10926792, \"y\": 0.4828604, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.095250055, \"y\": 0.65991616, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.104851894, \"y\": 0.6842645, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.12081572, \"y\": 0.71900976, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.12836349, \"y\": 0.7324452, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.12051879, \"y\": 0.7604618, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.110560834, \"y\": 0.80117565, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.13680358, \"y\": 0.8218284, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.2308929, \"y\": 0.48776552, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.22771391, \"y\": 0.45945382, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.20772786, \"y\": 0.6265242, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.23435974, \"y\": 0.47841263, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.2314856, \"y\": 0.4292004, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.22427182, \"y\": 0.22815874, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.21428339, \"y\": 0.2399715, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.2223332, \"y\": 0.21400864, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.21889023, \"y\": 0.16468468, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.22188364, \"y\": 0.14048208, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463345}, {\"x\": 0.25941673, \"y\": 0.12783326, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.29701465, \"y\": 0.11531867, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.32374588, \"y\": 0.28843403, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.3406515, \"y\": 0.4196514, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.33094376, \"y\": 0.37369162, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.31628093, \"y\": 0.35190794, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.30138394, \"y\": 0.33830935, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.33457088, \"y\": 0.4930466, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.3326512, \"y\": 0.52273905, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.31617248, \"y\": 0.5145963, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.3094948, \"y\": 0.5579588, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.31745678, \"y\": 0.61377, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.31048492, \"y\": 0.66084075, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.30370706, \"y\": 0.6970918, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.29077548, \"y\": 0.7385659, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.31014127, \"y\": 0.7846514, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.32345128, \"y\": 0.804806, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.34097725, \"y\": 0.8169924, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.38784987, \"y\": 0.2573734, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.3879763, \"y\": 0.3329701, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.3887523, \"y\": 0.3625502, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.3960681, \"y\": 0.41019428, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.4048633, \"y\": 0.45165205, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.5042078, \"y\": 0.12090711, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.4249317, \"y\": 0.24529888, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.4162804, \"y\": 0.25638896, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.4407988, \"y\": 0.07484294, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.43591058, \"y\": 0.062490698, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.43058056, \"y\": 0.10242199, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.4515829, \"y\": 0.105197504, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.47386804, \"y\": 0.10384536, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.48564407, \"y\": 0.11601862, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.4930998, \"y\": 0.14374265, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.50026244, \"y\": 0.18821466, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.50722307, \"y\": 0.22778237, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.4923736, \"y\": 0.3058165, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.48770696, \"y\": 0.33227992, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.49244946, \"y\": 0.3692628, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.51736045, \"y\": 0.61456376, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.5104292, \"y\": 0.6522994, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.48428825, \"y\": 0.6487608, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.4875526, \"y\": 0.67641044, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.5006602, \"y\": 0.7141697, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.50593156, \"y\": 0.7350233, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.5045427, \"y\": 0.7607595, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.4899375, \"y\": 0.79118717, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.54320425, \"y\": 0.5698705, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.5627712, \"y\": 0.57803226, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.57522833, \"y\": 0.5917557, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.5843127, \"y\": 0.57373583, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.57864183, \"y\": 0.5521839, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.56597346, \"y\": 0.5306907, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.6767948, \"y\": 0.19968933, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.6509771, \"y\": 0.2174298, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.6214953, \"y\": 0.24593745, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.6029337, \"y\": 0.26177907, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.69402856, \"y\": 0.063440256, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.6986606, \"y\": 0.048750438, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.6539395, \"y\": 0.10451567, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.6372139, \"y\": 0.12087135, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.6567032, \"y\": 0.0984376, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463346}, {\"x\": 0.67191267, \"y\": 0.09187164, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.7207255, \"y\": 0.23547137, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.6899314, \"y\": 0.71732813, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.6892538, \"y\": 0.73664236, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.6184922, \"y\": 0.89945716, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.5173865, \"y\": 0.9725909, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.64419484, \"y\": 0.7139346, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.6376781, \"y\": 0.77459365, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.42989233, \"y\": 0.97516316, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.8273194, \"y\": 0.36126238, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.84130645, \"y\": 0.32687008, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.8538108, \"y\": 0.2931429, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.8022272, \"y\": 0.5569279, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.89929354, \"y\": 0.07787815, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.90291774, \"y\": 0.060107186, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.8923399, \"y\": 0.02908912, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.8838677, \"y\": 0.017623339, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.86377555, \"y\": 0.039430268, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.88818777, \"y\": 0.079400904, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.89915717, \"y\": 0.086223006, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.9024262, \"y\": 0.11347034, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.904541, \"y\": 0.27764142, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.89993596, \"y\": 0.30435023, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.8937827, \"y\": 0.34265912, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.9839248, \"y\": 0.52680016, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.9884589, \"y\": 0.4732695, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.9903559, \"y\": 0.44934556, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.9489047, \"y\": 0.48928666, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.9179489, \"y\": 0.53125775, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.89888597, \"y\": 0.5597109, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}, {\"x\": 0.90785944, \"y\": 0.8889355, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750660463347}]",
                        "trackingAccuracy": 12.93,
                        "averageError": 0.1935,
                        "reactionTime": 0,
                        "smoothnessScore": 0,
                        "completionRate": 100,
                        "bezierCurveData": "[{\"endPoint\": [0.2, 0.1], \"startPoint\": [0.1, 0.1], \"controlPoint1\": [0.1, 0.9], \"controlPoint2\": [0.2, 0.9]}]",
                        "followCount": 1,
                        "successfulFollows": 0,
                        "pathLength": 8,
                        "actualPathLength": 24.82,
                        "velocityConsistency": 0,
                        "calibrationParams": "\"标准校准\"",
                        "environmentInfo": "\"室内光线充足\"",
                        "notes": "追随能力测试",
                        "imageUrl": null,
                        "createdAt": "2025-06-23 14:34:41",
                        "updatedAt": "2025-06-23 14:34:41"
                      },
                      {
                        "id": 7,
                        "recordId": 40,
                        "patientId": 34,
                        "patientName": "韩",
                        "inpatientNum": "111",
                        "caseCardNum": "111",
                        "deviceId": 12,
                        "deviceName": null,
                        "deviceSn": "DD92443A006",
                        "operatorId": 0,
                        "operatorName": null,
                        "testType": "FOLLOW_ABILITY",
                        "testSequence": "02",
                        "testDate": "2025-06-23 13:19:15",
                        "duration": 60000,
                        "status": "COMPLETED",
                        "statusDesc": "已完成",
                        "followPathPoints": "[[[0.1, 0.1], [0.1, 0.9], [0.2, 0.9], [0.2, 0.1], [0.3, 0.1], [0.3, 0.9], [0.4, 0.9], [0.4, 0.1], [0.5, 0.1], [0.5, 0.9], [0.6, 0.9], [0.6, 0.1], [0.7, 0.1], [0.7, 0.9], [0.8, 0.9], [0.8, 0.1], [0.9, 0.1], [0.9, 0.9]]]",
                        "actualGazePoints": "[{\"x\": 0.0, \"y\": 0.0, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.64472497, \"y\": 0.44176972, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.68938255, \"y\": 0.44277248, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.74681973, \"y\": 0.42154783, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.76878893, \"y\": 0.42040095, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.82324165, \"y\": 0.32815015, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.9194261, \"y\": 0.19240156, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.890419, \"y\": 0.3629318, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.92199636, \"y\": 0.4060204, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.9339752, \"y\": 0.41214713, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.9281327, \"y\": 0.4773666, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.9229415, \"y\": 0.5071422, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.9169835, \"y\": 0.53281623, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.9108647, \"y\": 0.5599515, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.9069196, \"y\": 0.58769655, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.9044206, \"y\": 0.66488653, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.8973434, \"y\": 0.70701087, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.89688087, \"y\": 0.74585533, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.89595413, \"y\": 0.77171963, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.87640065, \"y\": 0.7963021, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.8297651, \"y\": 0.79973537, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.762982, \"y\": 0.8071622, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.7344267, \"y\": 0.81026, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.70881844, \"y\": 0.81818223, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.6828981, \"y\": 0.8283973, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.6609351, \"y\": 0.8356199, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.6373998, \"y\": 0.82919353, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.6165739, \"y\": 0.8214086, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.56777686, \"y\": 0.8123281, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5326288, \"y\": 0.8099866, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.48988402, \"y\": 0.81556845, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.44828483, \"y\": 0.8110759, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.4310427, \"y\": 0.80396867, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.40185663, \"y\": 0.8062614, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.377461, \"y\": 0.8122993, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.33727407, \"y\": 0.81551313, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.2942255, \"y\": 0.817173, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.2652076, \"y\": 0.819845, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.24327998, \"y\": 0.82728636, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.21390313, \"y\": 0.8409286, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.19105494, \"y\": 0.8307383, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.16589797, \"y\": 0.81302005, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.15214543, \"y\": 0.7917204, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.15663345, \"y\": 0.7552392, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.17877561, \"y\": 0.7692363, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.2149595, \"y\": 0.77268034, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.24204716, \"y\": 0.7731466, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.26673418, \"y\": 0.76462716, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.29425216, \"y\": 0.7536466, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.58846575, \"y\": 0.74059874, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.49752668, \"y\": 0.73882926, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.47687215, \"y\": 0.73966396, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5034452, \"y\": 0.72690284, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.52091867, \"y\": 0.7163006, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.55461454, \"y\": 0.72582376, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5680053, \"y\": 0.7391987, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.59208465, \"y\": 0.74737847, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.66413873, \"y\": 0.7590942, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.70851785, \"y\": 0.76155937, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.72343934, \"y\": 0.77179766, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.79123783, \"y\": 0.79008245, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.8275268, \"y\": 0.79416007, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.8485148, \"y\": 0.7953836, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.865209, \"y\": 0.7841574, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.8751408, \"y\": 0.748078, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.7503468, \"y\": 0.6875555, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.72667557, \"y\": 0.68664503, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.7357766, \"y\": 0.68855494, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.72281826, \"y\": 0.6949348, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.6957373, \"y\": 0.68831813, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.66426736, \"y\": 0.68424934, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.64832556, \"y\": 0.6739925, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.6258045, \"y\": 0.6562381, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.61190224, \"y\": 0.65192413, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5848348, \"y\": 0.63771904, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.56963986, \"y\": 0.63012624, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.52769035, \"y\": 0.6233784, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.39027515, \"y\": 0.6349464, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.36525473, \"y\": 0.6423807, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.3924841, \"y\": 0.6281932, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.339879, \"y\": 0.619267, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.32039115, \"y\": 0.6210115, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.29583785, \"y\": 0.6323363, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.17473257, \"y\": 0.6641128, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.21942954, \"y\": 0.6363562, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.19221222, \"y\": 0.6630515, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.16148305, \"y\": 0.6680634, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.12868777, \"y\": 0.6708553, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.115098596, \"y\": 0.6633988, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.1963565, \"y\": 0.59197474, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.22518632, \"y\": 0.5791538, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.2556055, \"y\": 0.57358825, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.3837057, \"y\": 0.5013738, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.3622378, \"y\": 0.52057344, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.3970945, \"y\": 0.52188444, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.43230423, \"y\": 0.5260842, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.45814288, \"y\": 0.53873426, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5067821, \"y\": 0.5425372, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5296011, \"y\": 0.54551154, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5577415, \"y\": 0.5340875, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5821078, \"y\": 0.5286321, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.6075593, \"y\": 0.5254519, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.63763005, \"y\": 0.5332184, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.6616006, \"y\": 0.5383926, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.72323006, \"y\": 0.5681081, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.75270385, \"y\": 0.5793163, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.7736392, \"y\": 0.5636827, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.83299375, \"y\": 0.578286, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.8627632, \"y\": 0.57937795, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.87910724, \"y\": 0.5745041, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.89879715, \"y\": 0.5226739, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.7395944, \"y\": 0.4118148, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5141601, \"y\": 0.39507505, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.3110351, \"y\": 0.40683907, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5713565, \"y\": 0.44791445, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5886252, \"y\": 0.42775002, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.59847575, \"y\": 0.42211366, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5484562, \"y\": 0.41037259, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.50331163, \"y\": 0.41487563, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.48149484, \"y\": 0.41558462, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.44365782, \"y\": 0.4200455, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.39920282, \"y\": 0.44029436, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.37735367, \"y\": 0.43183118, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.35800046, \"y\": 0.41889378, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.30436242, \"y\": 0.41913965, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.27560088, \"y\": 0.42308015, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.25040168, \"y\": 0.41395706, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.1548335, \"y\": 0.44917172, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.13458803, \"y\": 0.45359182, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.12467757, \"y\": 0.44396186, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.13612105, \"y\": 0.42875543, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.1532923, \"y\": 0.40878564, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.17615424, \"y\": 0.39691627, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.19514619, \"y\": 0.3855491, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.2977009, \"y\": 0.34188107, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.31455427, \"y\": 0.32956174, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.34458458, \"y\": 0.32944122, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.36593074, \"y\": 0.32952723, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.3918568, \"y\": 0.33406925, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.52308625, \"y\": 0.35287136, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5355204, \"y\": 0.35153118, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.5540715, \"y\": 0.3401387, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.7898493, \"y\": 0.3443251, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.7520038, \"y\": 0.34027505, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955239}, {\"x\": 0.72588396, \"y\": 0.3327939, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.94298494, \"y\": 0.40035513, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.9327748, \"y\": 0.37305292, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.91451436, \"y\": 0.3501329, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.90890855, \"y\": 0.31604066, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.93085593, \"y\": 0.3107325, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.36713138, \"y\": 0.25306693, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.06603803, \"y\": 0.34192958, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.07909633, \"y\": 0.325398, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.021156983, \"y\": 0.4232726, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.011085507, \"y\": 0.48001227, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.006270353, \"y\": 0.51632965, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.0076412694, \"y\": 0.4324901, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.10219939, \"y\": 0.29864928, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.17955945, \"y\": 0.28345552, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.45592135, \"y\": 0.23928557, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.33033115, \"y\": 0.2591363, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.026505407, \"y\": 0.40449935, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.015473502, \"y\": 0.39582592, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.22854222, \"y\": 0.27171448, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.19501999, \"y\": 0.2512256, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.1722143, \"y\": 0.24554855, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.13387135, \"y\": 0.2614178, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.12402836, \"y\": 0.26758382, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.13549769, \"y\": 0.24432136, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.15289302, \"y\": 0.23050027, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.17151317, \"y\": 0.2318187, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5577648, \"y\": 0.14402717, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.33516216, \"y\": 0.17455553, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.41243306, \"y\": 0.1610247, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5816258, \"y\": 0.12058981, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.549778, \"y\": 0.13949046, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5327477, \"y\": 0.15790945, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5369413, \"y\": 0.17412192, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.55769277, \"y\": 0.16215944, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.60077846, \"y\": 0.15555227, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.6475301, \"y\": 0.15256426, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.6685517, \"y\": 0.1481603, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.68402183, \"y\": 0.14063378, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.82407147, \"y\": 0.13653432, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.8573222, \"y\": 0.13100806, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.88715386, \"y\": 0.12491346, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.9082795, \"y\": 0.1170763, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.93497133, \"y\": 0.13160445, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.9424041, \"y\": 0.13006, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.9247419, \"y\": 0.12144057, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.496022, \"y\": 0.05595576, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.7445955, \"y\": 0.07207102, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.25403988, \"y\": 0.113673344, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.2212877, \"y\": 0.12164599, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.19718683, \"y\": 0.11827437, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.17743623, \"y\": 0.10282624, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.53491795, \"y\": 0.07517696, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.18598013, \"y\": 0.12679008, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.17282404, \"y\": 0.13300109, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.32716227, \"y\": 0.10830331, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.25889266, \"y\": 0.10126096, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.21796547, \"y\": 0.107831486, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.1964794, \"y\": 0.12237044, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.18602397, \"y\": 0.14552401, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.14991748, \"y\": 0.16133077, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.13232194, \"y\": 0.17383528, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.12300031, \"y\": 0.25303462, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.12815496, \"y\": 0.31948742, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.1175059, \"y\": 0.3402157, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.11407919, \"y\": 0.38795316, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.11453684, \"y\": 0.4336989, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.11313706, \"y\": 0.46494892, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.11583036, \"y\": 0.5027296, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.121618286, \"y\": 0.53598756, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.12023874, \"y\": 0.56088763, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.11840297, \"y\": 0.5885705, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.116987914, \"y\": 0.62158763, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.11391981, \"y\": 0.65530515, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.1138999, \"y\": 0.7164354, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.11144252, \"y\": 0.73966956, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.119904384, \"y\": 0.77484334, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.12409373, \"y\": 0.794684, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.13497436, \"y\": 0.81363374, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.14065833, \"y\": 0.8432174, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.14057511, \"y\": 0.8769531, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.4076841, \"y\": 0.8273152, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.42914104, \"y\": 0.82135445, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.29812235, \"y\": 0.80765665, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.2768545, \"y\": 0.816108, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.26247847, \"y\": 0.78382814, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.24643168, \"y\": 0.7138249, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.23909491, \"y\": 0.6645641, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.24140956, \"y\": 0.633323, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.24560085, \"y\": 0.58787525, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.22457814, \"y\": 0.5329494, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.2095977, \"y\": 0.49316007, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.20717858, \"y\": 0.46110016, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.20490286, \"y\": 0.43161267, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.20232525, \"y\": 0.41315886, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.21799886, \"y\": 0.3730119, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.24956314, \"y\": 0.2686093, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.23801559, \"y\": 0.22785085, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.23316492, \"y\": 0.2059518, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.24316527, \"y\": 0.16514774, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.25409722, \"y\": 0.14588642, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.2731681, \"y\": 0.1335597, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.3084175, \"y\": 0.11938886, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.31355247, \"y\": 0.16820616, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.31507444, \"y\": 0.19412377, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.31635976, \"y\": 0.23769924, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.31505376, \"y\": 0.26722577, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.31501314, \"y\": 0.29237074, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.32009706, \"y\": 0.33836734, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.3232511, \"y\": 0.3671964, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.31507316, \"y\": 0.4331788, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.3100087, \"y\": 0.48122433, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.30532008, \"y\": 0.52222395, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.30271387, \"y\": 0.5501712, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.30585733, \"y\": 0.57421595, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.3098417, \"y\": 0.60715973, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.3125149, \"y\": 0.6429257, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.32050568, \"y\": 0.6802563, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.32628754, \"y\": 0.72622234, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.32323486, \"y\": 0.7877582, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.3660239, \"y\": 0.77084935, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.38303357, \"y\": 0.73968947, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.4379393, \"y\": 0.5718776, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.41239715, \"y\": 0.38047165, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.41328847, \"y\": 0.35256994, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.41461515, \"y\": 0.33764678, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.4354981, \"y\": 0.3201478, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.42928502, \"y\": 0.2908518, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.44070065, \"y\": 0.24776241, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.44384736, \"y\": 0.22995898, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.43503183, \"y\": 0.20942768, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.4198729, \"y\": 0.18751872, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.41245428, \"y\": 0.17210346, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.44151485, \"y\": 0.15551618, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.46190637, \"y\": 0.14184467, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5127984, \"y\": 0.1585237, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5313226, \"y\": 0.16523862, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5319397, \"y\": 0.19387148, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5300795, \"y\": 0.23508173, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5271099, \"y\": 0.26811466, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.51768327, \"y\": 0.30495477, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5171968, \"y\": 0.33431095, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.51239926, \"y\": 0.38569272, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.50454324, \"y\": 0.43009254, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5003578, \"y\": 0.46348074, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.49931887, \"y\": 0.49372593, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.49585468, \"y\": 0.53212136, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.4887435, \"y\": 0.55778605, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5014984, \"y\": 0.5921145, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.51422274, \"y\": 0.6341177, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5134935, \"y\": 0.6641422, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5005082, \"y\": 0.70780706, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.49110782, \"y\": 0.73667765, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.48166627, \"y\": 0.77417284, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.488058, \"y\": 0.8310175, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.6198125, \"y\": 0.55263835, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.6204323, \"y\": 0.46656302, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.61176276, \"y\": 0.31186044, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.616851, \"y\": 0.34152365, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.58609116, \"y\": 0.57915837, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.5753097, \"y\": 0.61558956, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.6029616, \"y\": 0.47882333, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.60778624, \"y\": 0.44682384, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.6133837, \"y\": 0.41700217, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955240}, {\"x\": 0.606693, \"y\": 0.36835527, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.59462106, \"y\": 0.34303236, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.6076128, \"y\": 0.2828361, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.6236349, \"y\": 0.20477633, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.6263447, \"y\": 0.16010666, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.6294546, \"y\": 0.12503843, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.65115285, \"y\": 0.119703315, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.67716897, \"y\": 0.11238737, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.72621036, \"y\": 0.3238038, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.70798236, \"y\": 0.2759367, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.7026268, \"y\": 0.3320027, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.7105089, \"y\": 0.42891353, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.7094519, \"y\": 0.48544943, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.6905567, \"y\": 0.51463777, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.6836047, \"y\": 0.5436547, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.67986447, \"y\": 0.5642541, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.66964877, \"y\": 0.6264646, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.6673208, \"y\": 0.67225575, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.65774345, \"y\": 0.69680125, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.6546067, \"y\": 0.7291218, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.6670718, \"y\": 0.7753314, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.6524838, \"y\": 0.7956693, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.6373687, \"y\": 0.8520099, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.6736958, \"y\": 0.8774576, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.69580036, \"y\": 0.8989406, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.70298254, \"y\": 0.87993, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.75237346, \"y\": 0.76614237, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.76422524, \"y\": 0.7502322, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.77012634, \"y\": 0.72686756, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.8215153, \"y\": 0.49871352, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.82309896, \"y\": 0.47584856, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.81232, \"y\": 0.43508273, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.80974644, \"y\": 0.41262996, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.8152584, \"y\": 0.38207966, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.82033587, \"y\": 0.34940168, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.8206957, \"y\": 0.32346398, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.81705576, \"y\": 0.29857117, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.82195735, \"y\": 0.2627506, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.8330035, \"y\": 0.20304665, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.8319549, \"y\": 0.16685191, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.83686185, \"y\": 0.13223907, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.8412279, \"y\": 0.12482965, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.8784336, \"y\": 0.13823417, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.8956339, \"y\": 0.12528543, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.9211693, \"y\": 0.16324174, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.93316364, \"y\": 0.19488238, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.93062115, \"y\": 0.24021229, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.90962374, \"y\": 0.29422405, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.9017616, \"y\": 0.30954137, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.9097919, \"y\": 0.36365497, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.91561955, \"y\": 0.4459282, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.916659, \"y\": 0.47705728, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.92087543, \"y\": 0.4992988, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.8931371, \"y\": 0.55340147, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.8808836, \"y\": 0.57405007, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.87229335, \"y\": 0.5959991, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.87559855, \"y\": 0.61639106, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}, {\"x\": 0.8872236, \"y\": 0.67885584, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750655955241}]",
                        "trackingAccuracy": 9.55,
                        "averageError": 0.2067,
                        "reactionTime": 0,
                        "smoothnessScore": 0,
                        "completionRate": 100,
                        "bezierCurveData": "[{\"endPoint\": [0.2, 0.1], \"startPoint\": [0.1, 0.1], \"controlPoint1\": [0.1, 0.9], \"controlPoint2\": [0.2, 0.9]}]",
                        "followCount": 1,
                        "successfulFollows": 0,
                        "pathLength": 8,
                        "actualPathLength": 22.33,
                        "velocityConsistency": 0,
                        "calibrationParams": "\"标准校准\"",
                        "environmentInfo": "\"室内光线充足\"",
                        "notes": "追随能力测试",
                        "imageUrl": null,
                        "createdAt": "2025-06-23 13:20:19",
                        "updatedAt": "2025-06-23 13:20:19"
                      },
                      {
                        "id": 6,
                        "recordId": 39,
                        "patientId": 33,
                        "patientName": "hz",
                        "inpatientNum": "1234",
                        "caseCardNum": "1234",
                        "deviceId": 12,
                        "deviceName": null,
                        "deviceSn": "DD92443A006",
                        "operatorId": 0,
                        "operatorName": null,
                        "testType": "FOLLOW_ABILITY",
                        "testSequence": "02",
                        "testDate": "2025-06-23 11:26:02",
                        "duration": 60000,
                        "status": "COMPLETED",
                        "statusDesc": "已完成",
                        "followPathPoints": "[[[0.1, 0.1], [0.1, 0.9], [0.2, 0.9], [0.2, 0.1], [0.3, 0.1], [0.3, 0.9], [0.4, 0.9], [0.4, 0.1], [0.5, 0.1], [0.5, 0.9], [0.6, 0.9], [0.6, 0.1], [0.7, 0.1], [0.7, 0.9], [0.8, 0.9], [0.8, 0.1], [0.9, 0.1], [0.9, 0.9]]]",
                        "actualGazePoints": "[{\"x\": 0.0, \"y\": 0.0, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.6393203, \"y\": 0.47518048, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.6592712, \"y\": 0.48456037, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.6798427, \"y\": 0.4978823, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.70955175, \"y\": 0.45706648, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.732698, \"y\": 0.44215012, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.73971885, \"y\": 0.47240165, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.8216369, \"y\": 0.543741, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.8327482, \"y\": 0.56167656, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.82559544, \"y\": 0.5974554, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.81933075, \"y\": 0.6186323, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.8142732, \"y\": 0.6411107, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.8162896, \"y\": 0.69061124, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.8107036, \"y\": 0.7467298, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.80267, \"y\": 0.7754365, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.7936728, \"y\": 0.8462142, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.7865608, \"y\": 0.92112315, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.8070022, \"y\": 0.90915704, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.7693323, \"y\": 0.9258094, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.52953404, \"y\": 0.9464009, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.6672643, \"y\": 0.8704479, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.6901247, \"y\": 0.8407795, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.608521, \"y\": 0.84433055, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.067496404, \"y\": 0.99576825, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.38842538, \"y\": 0.983416, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.45073077, \"y\": 0.97198045, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.4669093, \"y\": 0.9587246, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.279906, \"y\": 0.9405855, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.26138344, \"y\": 0.96370405, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.31472093, \"y\": 0.9456339, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.34611875, \"y\": 0.94303733, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.3701263, \"y\": 0.92549324, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.30758512, \"y\": 0.9102327, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162688}, {\"x\": 0.27322528, \"y\": 0.9099192, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.24635485, \"y\": 0.9191009, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.15570909, \"y\": 0.9007492, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.15030931, \"y\": 0.907827, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.1635796, \"y\": 0.8713363, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.17086527, \"y\": 0.84552604, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.17938218, \"y\": 0.8266868, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.1984598, \"y\": 0.7916586, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.77037615, \"y\": 0.87194324, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.36345893, \"y\": 0.83198065, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.8062857, \"y\": 0.85731006, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.9272664, \"y\": 0.87362474, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.5961928, \"y\": 0.78441364, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.57447416, \"y\": 0.79399604, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.540087, \"y\": 0.79513824, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.5113905, \"y\": 0.81587064, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.49938747, \"y\": 0.8312632, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.6519071, \"y\": 0.7838067, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.6194535, \"y\": 0.76639897, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.6639595, \"y\": 0.785819, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.6837359, \"y\": 0.79420364, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.72003794, \"y\": 0.7879384, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.7493369, \"y\": 0.7816718, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.78854597, \"y\": 0.7794093, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.82239497, \"y\": 0.7434711, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.8129171, \"y\": 0.71597517, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.79735637, \"y\": 0.71139693, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.20265895, \"y\": 0.68770254, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.4413852, \"y\": 0.6577827, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.60154545, \"y\": 0.71237445, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.05433584, \"y\": 0.7552559, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.02809284, \"y\": 0.7758398, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.24168223, \"y\": 0.67528164, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.37814248, \"y\": 0.63271415, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.44440627, \"y\": 0.66289806, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.4507102, \"y\": 0.67123115, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.3869418, \"y\": 0.67513764, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.15675604, \"y\": 0.7155859, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.2724015, \"y\": 0.69323564, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.29936486, \"y\": 0.69164777, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.3086892, \"y\": 0.6863872, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.19751637, \"y\": 0.6384147, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.21332817, \"y\": 0.6630737, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.20269409, \"y\": 0.6741356, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.16881528, \"y\": 0.68713075, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.1828095, \"y\": 0.61345696, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.19364509, \"y\": 0.5842979, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.21034938, \"y\": 0.603226, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.2518765, \"y\": 0.6019159, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.5925154, \"y\": 0.63972443, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.61337614, \"y\": 0.63794756, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.57910633, \"y\": 0.63587636, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.39512444, \"y\": 0.60514665, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.96975034, \"y\": 0.5630179, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.48070073, \"y\": 0.55175793, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.62026644, \"y\": 0.5277745, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.64118826, \"y\": 0.5216677, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.95543903, \"y\": 0.65603125, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.98297644, \"y\": 0.73265785, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.96486694, \"y\": 0.69425887, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.9251908, \"y\": 0.6753522, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.9048712, \"y\": 0.66498935, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.84066796, \"y\": 0.6291242, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.8111187, \"y\": 0.6191588, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.5556033, \"y\": 0.24323855, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.6974338, \"y\": 0.2506855, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.013941949, \"y\": 0.41397464, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.5936531, \"y\": 0.44007623, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.5751139, \"y\": 0.45066917, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.5476749, \"y\": 0.48142204, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.52047175, \"y\": 0.50996524, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.49097753, \"y\": 0.5087707, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.479123, \"y\": 0.49821323, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.20063135, \"y\": 0.4497107, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.14325972, \"y\": 0.43559018, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.14045104, \"y\": 0.41056442, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.1486991, \"y\": 0.3747305, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.22899216, \"y\": 0.4631868, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.20819643, \"y\": 0.49237776, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.19395836, \"y\": 0.5032563, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.18376449, \"y\": 0.52404416, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.5146233, \"y\": 0.042799853, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.26277396, \"y\": 0.272367, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.25312468, \"y\": 0.30377805, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.23767543, \"y\": 0.38214538, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.23513499, \"y\": 0.4316333, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.90522975, \"y\": 0.5510224, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.9611774, \"y\": 0.59368134, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.9771578, \"y\": 0.6132942, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.7713043, \"y\": 0.45339358, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.47911003, \"y\": 0.4527958, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.51054114, \"y\": 0.4303658, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.60763645, \"y\": 0.35674602, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.61454684, \"y\": 0.37640738, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.63047063, \"y\": 0.39174512, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.65879536, \"y\": 0.40338635, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.6827878, \"y\": 0.41969734, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.7079707, \"y\": 0.43019673, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.72677433, \"y\": 0.42150956, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162689}, {\"x\": 0.79282296, \"y\": 0.42639554, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.81402737, \"y\": 0.41640338, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.79341894, \"y\": 0.395708, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.6960873, \"y\": 0.38461486, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.17577595, \"y\": 0.37367952, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.15387304, \"y\": 0.36076307, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.5734814, \"y\": 0.33931422, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.5078649, \"y\": 0.3509913, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.17261417, \"y\": 0.3785521, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.50468314, \"y\": 0.37420171, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.44207668, \"y\": 0.36224324, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.41816446, \"y\": 0.36281997, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.38654262, \"y\": 0.35130042, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.3573686, \"y\": 0.36356193, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.3520721, \"y\": 0.37813482, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.34325463, \"y\": 0.39267594, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.19785623, \"y\": 0.3322528, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.17347106, \"y\": 0.3159641, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.19144742, \"y\": 0.35924038, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.19900692, \"y\": 0.37696144, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.1748442, \"y\": 0.36954293, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.16747402, \"y\": 0.36347592, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.16883235, \"y\": 0.2583512, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.17373836, \"y\": 0.24306564, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.18601269, \"y\": 0.22799158, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.79814667, \"y\": 0.25644624, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.9787006, \"y\": 0.40672064, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.50913537, \"y\": 0.29920223, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.4754982, \"y\": 0.2880161, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.44851267, \"y\": 0.2887628, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.4339091, \"y\": 0.29383028, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.59136456, \"y\": 0.26152498, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.60882825, \"y\": 0.26063532, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.59279996, \"y\": 0.26949194, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.57632047, \"y\": 0.28689432, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.7033372, \"y\": 0.2634419, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.71613544, \"y\": 0.2669706, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.78704506, \"y\": 0.25879693, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.82816714, \"y\": 0.24147208, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.84484184, \"y\": 0.22957346, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.85266274, \"y\": 0.21602838, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.8179028, \"y\": 0.23010293, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.7968161, \"y\": 0.24634269, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.6808306, \"y\": 0.23598066, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.7512402, \"y\": 0.2276867, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.7896845, \"y\": 0.22843835, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.7617644, \"y\": 0.2098315, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.7308076, \"y\": 0.21792273, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.08819555, \"y\": 0.23079754, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.07818392, \"y\": 0.24179694, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.21016723, \"y\": 0.19347924, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.5054136, \"y\": 0.20566082, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.519407, \"y\": 0.21579166, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.49731594, \"y\": 0.23395108, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.47811696, \"y\": 0.23533724, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.46431097, \"y\": 0.24668851, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.24900657, \"y\": 0.239219, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.23224261, \"y\": 0.23228458, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.27109653, \"y\": 0.22476251, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.28228572, \"y\": 0.21908742, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.23406142, \"y\": 0.22974297, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.2157794, \"y\": 0.22430219, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.17652644, \"y\": 0.22671394, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.15184553, \"y\": 0.23305392, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.14939839, \"y\": 0.26644278, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.15267108, \"y\": 0.2932164, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.1570028, \"y\": 0.30805218, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.16305102, \"y\": 0.3415692, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.16131303, \"y\": 0.3776327, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.1566057, \"y\": 0.4722106, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.1563409, \"y\": 0.4925342, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.14466737, \"y\": 0.53667456, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.15558434, \"y\": 0.5657201, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.17279816, \"y\": 0.6035774, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.1719791, \"y\": 0.6261358, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.16928472, \"y\": 0.6895264, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.16598254, \"y\": 0.71484053, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.1618652, \"y\": 0.7759204, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.16859888, \"y\": 0.84296894, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.19220905, \"y\": 0.8798953, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.20668674, \"y\": 0.9038958, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.6093616, \"y\": 0.94016516, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.90702695, \"y\": 0.99378043, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.85436344, \"y\": 0.95173854, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.2689939, \"y\": 0.58464855, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.26128283, \"y\": 0.59387934, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.2560147, \"y\": 0.56182784, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.25335056, \"y\": 0.53961813, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.2480218, \"y\": 0.47092763, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.24704376, \"y\": 0.42408255, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.24552478, \"y\": 0.39475355, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.24305674, \"y\": 0.36720657, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.24346316, \"y\": 0.32857797, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.24718085, \"y\": 0.30236, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.26872325, \"y\": 0.26508996, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.28208274, \"y\": 0.2582395, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.30782172, \"y\": 0.31522608, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.32244098, \"y\": 0.35176784, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.32688344, \"y\": 0.37673703, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.33000147, \"y\": 0.4053969, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.33502328, \"y\": 0.43792763, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162690}, {\"x\": 0.3305895, \"y\": 0.4628967, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.33101463, \"y\": 0.49241692, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.3283686, \"y\": 0.5277252, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.3213174, \"y\": 0.57569146, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.31630155, \"y\": 0.61480564, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.31863883, \"y\": 0.6506524, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.31778374, \"y\": 0.680152, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.32081807, \"y\": 0.7371234, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.32629868, \"y\": 0.7797673, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.33079547, \"y\": 0.8072738, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.3246945, \"y\": 0.88944083, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.35960212, \"y\": 0.8858299, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.3710528, \"y\": 0.899775, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.37652743, \"y\": 0.90833426, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.39782673, \"y\": 0.44844618, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.41637707, \"y\": 0.49416393, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.41560704, \"y\": 0.52918434, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.3400717, \"y\": 0.58854145, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.38650596, \"y\": 0.55201936, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.42334914, \"y\": 0.51169056, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.42363915, \"y\": 0.4733692, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.41990626, \"y\": 0.45285848, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.4222362, \"y\": 0.41830316, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.40069306, \"y\": 0.3636298, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.39087233, \"y\": 0.34313545, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.4080717, \"y\": 0.3539056, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.45733008, \"y\": 0.5305159, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.45727488, \"y\": 0.5561976, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.47585985, \"y\": 0.29846895, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.47549507, \"y\": 0.2624972, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.49483994, \"y\": 0.24490751, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.51811206, \"y\": 0.23647004, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.508477, \"y\": 0.26594308, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.502602, \"y\": 0.3046708, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.49326098, \"y\": 0.33695948, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.48768306, \"y\": 0.36586797, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.48310983, \"y\": 0.40361327, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.47757944, \"y\": 0.44481587, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.4796892, \"y\": 0.46600124, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.47724953, \"y\": 0.515416, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.47377518, \"y\": 0.5466772, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.47261304, \"y\": 0.58286333, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.47667575, \"y\": 0.6052064, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.48336673, \"y\": 0.67655104, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.48743874, \"y\": 0.7140987, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.47985637, \"y\": 0.75374264, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.4755728, \"y\": 0.76687837, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.50984514, \"y\": 0.81854856, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.5132463, \"y\": 0.85075414, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.59413135, \"y\": 0.66499656, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6034989, \"y\": 0.59156984, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.60169804, \"y\": 0.5374869, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.59186715, \"y\": 0.5384674, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.58864933, \"y\": 0.50095576, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.5892504, \"y\": 0.4736001, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.5930837, \"y\": 0.44849178, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.59230375, \"y\": 0.4090006, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.59219444, \"y\": 0.381221, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.57795435, \"y\": 0.40570605, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.5657762, \"y\": 0.381148, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.5713601, \"y\": 0.31719953, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.5681408, \"y\": 0.27263415, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.56532353, \"y\": 0.239568, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.5728415, \"y\": 0.22510764, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.606072, \"y\": 0.23364064, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6298828, \"y\": 0.24743219, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6532576, \"y\": 0.2707063, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6588752, \"y\": 0.3515135, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6498855, \"y\": 0.6546041, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6460042, \"y\": 0.6808811, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6544954, \"y\": 0.48346186, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6723602, \"y\": 0.6067698, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6439831, \"y\": 0.6676035, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6293053, \"y\": 0.7135277, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6168409, \"y\": 0.7621556, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.624981, \"y\": 0.78865343, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6266553, \"y\": 0.83680475, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6520517, \"y\": 0.8762019, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6687435, \"y\": 0.91062963, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.6878573, \"y\": 0.9236511, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.75830257, \"y\": 0.348488, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.7508591, \"y\": 0.39830512, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.7420111, \"y\": 0.43006337, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.7462575, \"y\": 0.3859333, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.76039106, \"y\": 0.3367119, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.7600482, \"y\": 0.31930646, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.78196096, \"y\": 0.30608547, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.7844808, \"y\": 0.26633567, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.7814803, \"y\": 0.23603018, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.80467165, \"y\": 0.2775343, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.89294356, \"y\": 0.2693819, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.8156295, \"y\": 0.45848593, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.88904154, \"y\": 0.4932858, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.91298485, \"y\": 0.47110102, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.88017505, \"y\": 0.49320543, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.7973403, \"y\": 0.90785253, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.7863343, \"y\": 0.94018584, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.8298391, \"y\": 0.9325583, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.8638959, \"y\": 0.9192572, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.90521675, \"y\": 0.77629113, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}, {\"x\": 0.8985041, \"y\": 0.7517117, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750649162691}]",
                        "trackingAccuracy": 7.78,
                        "averageError": 0.2172,
                        "reactionTime": 0,
                        "smoothnessScore": 0,
                        "completionRate": 100,
                        "bezierCurveData": "[{\"endPoint\": [0.2, 0.1], \"startPoint\": [0.1, 0.1], \"controlPoint1\": [0.1, 0.9], \"controlPoint2\": [0.2, 0.9]}]",
                        "followCount": 1,
                        "successfulFollows": 0,
                        "pathLength": 8,
                        "actualPathLength": 32.58,
                        "velocityConsistency": 0,
                        "calibrationParams": "\"标准校准\"",
                        "environmentInfo": "\"室内光线充足\"",
                        "notes": "追随能力测试",
                        "imageUrl": null,
                        "createdAt": "2025-06-23 11:26:16",
                        "updatedAt": "2025-06-23 11:26:16"
                      },
                      {
                        "id": 5,
                        "recordId": 22,
                        "patientId": 13,
                        "patientName": null,
                        "inpatientNum": null,
                        "caseCardNum": null,
                        "deviceId": 10,
                        "deviceName": null,
                        "deviceSn": "test-device",
                        "operatorId": 0,
                        "operatorName": null,
                        "testType": "FOLLOW_ABILITY",
                        "testSequence": "02",
                        "testDate": "2023-12-01 11:00:00",
                        "duration": 45000,
                        "status": "COMPLETED",
                        "statusDesc": "已完成",
                        "followPathPoints": "[[100, 100], [200, 200], [300, 150]]",
                        "actualGazePoints": "[[105, 98], [198, 205], [295, 148]]",
                        "trackingAccuracy": 92.5,
                        "averageError": 5.2,
                        "reactionTime": 150.5,
                        "smoothnessScore": 88.3,
                        "completionRate": 95,
                        "bezierCurveData": "{}",
                        "followCount": 20,
                        "successfulFollows": 18,
                        "pathLength": 500,
                        "actualPathLength": 485.2,
                        "velocityConsistency": 85.5,
                        "calibrationParams": "\"标准校准\"",
                        "environmentInfo": "\"室内光线充足\"",
                        "notes": "追随能力测试",
                        "imageUrl": null,
                        "createdAt": "2025-06-19 11:42:44",
                        "updatedAt": "2025-06-19 11:42:44"
                      },
                      {
                        "id": 4,
                        "recordId": 17,
                        "patientId": 13,
                        "patientName": null,
                        "inpatientNum": null,
                        "caseCardNum": null,
                        "deviceId": 7,
                        "deviceName": "眼动仪设备",
                        "deviceSn": "DEV002",
                        "operatorId": 0,
                        "operatorName": null,
                        "testType": "FOLLOW_ABILITY",
                        "testSequence": "02",
                        "testDate": "2023-12-01 11:00:00",
                        "duration": 45000,
                        "status": "COMPLETED",
                        "statusDesc": "已完成",
                        "followPathPoints": "[[100, 100], [200, 200], [300, 150]]",
                        "actualGazePoints": "[[105, 98], [198, 205], [295, 148]]",
                        "trackingAccuracy": 92.5,
                        "averageError": 5.2,
                        "reactionTime": 150.5,
                        "smoothnessScore": 88.3,
                        "completionRate": 95,
                        "bezierCurveData": "{}",
                        "followCount": 20,
                        "successfulFollows": 18,
                        "pathLength": 500,
                        "actualPathLength": 485.2,
                        "velocityConsistency": 85.5,
                        "calibrationParams": "\"标准校准\"",
                        "environmentInfo": "\"室内光线充足\"",
                        "notes": "追随能力测试",
                        "imageUrl": null,
                        "createdAt": "2025-06-19 11:08:43",
                        "updatedAt": "2025-06-19 11:08:43"
                      },
                      {
                        "id": 3,
                        "recordId": 15,
                        "patientId": 13,
                        "patientName": null,
                        "inpatientNum": null,
                        "caseCardNum": null,
                        "deviceId": 7,
                        "deviceName": "眼动仪设备",
                        "deviceSn": "DEV002",
                        "operatorId": 7,
                        "operatorName": "operator002",
                        "testType": "FOLLOW_ABILITY",
                        "testSequence": "02",
                        "testDate": "2023-12-01 11:00:00",
                        "duration": 45000,
                        "status": "COMPLETED",
                        "statusDesc": "已完成",
                        "followPathPoints": "[[100, 100], [200, 200], [300, 150]]",
                        "actualGazePoints": "[[105, 98], [198, 205], [295, 148]]",
                        "trackingAccuracy": 92.5,
                        "averageError": 5.2,
                        "reactionTime": 150.5,
                        "smoothnessScore": 88.3,
                        "completionRate": 95,
                        "bezierCurveData": "{}",
                        "followCount": 20,
                        "successfulFollows": 18,
                        "pathLength": 500,
                        "actualPathLength": 485.2,
                        "velocityConsistency": 85.5,
                        "calibrationParams": "\"标准校准\"",
                        "environmentInfo": "\"室内光线充足\"",
                        "notes": "追随能力测试",
                        "imageUrl": null,
                        "createdAt": "2025-06-19 11:00:49",
                        "updatedAt": "2025-06-19 11:00:49"
                      },
                      {
                        "id": 2,
                        "recordId": 11,
                        "patientId": 8,
                        "patientName": "李四",
                        "inpatientNum": "IN002",
                        "caseCardNum": "CC002",
                        "deviceId": 7,
                        "deviceName": "眼动仪设备",
                        "deviceSn": "DEV002",
                        "operatorId": 7,
                        "operatorName": "operator002",
                        "testType": "FOLLOW_ABILITY",
                        "testSequence": "02",
                        "testDate": "2023-12-01 11:00:00",
                        "duration": 45000,
                        "status": "COMPLETED",
                        "statusDesc": "已完成",
                        "followPathPoints": "[[100, 100], [200, 200], [300, 150]]",
                        "actualGazePoints": "[[105, 98], [198, 205], [295, 148]]",
                        "trackingAccuracy": 92.5,
                        "averageError": 5.2,
                        "reactionTime": 150.5,
                        "smoothnessScore": 88.3,
                        "completionRate": 95,
                        "bezierCurveData": "{}",
                        "followCount": 20,
                        "successfulFollows": 18,
                        "pathLength": 500,
                        "actualPathLength": 485.2,
                        "velocityConsistency": 85.5,
                        "calibrationParams": "\"标准校准\"",
                        "environmentInfo": "\"室内光线充足\"",
                        "notes": "追随能力测试",
                        "imageUrl": null,
                        "createdAt": "2025-06-18 22:08:07",
                        "updatedAt": "2025-06-18 22:08:07"
                      },
                      {
                        "id": 1,
                        "recordId": 6,
                        "patientId": 8,
                        "patientName": "李四",
                        "inpatientNum": "IN002",
                        "caseCardNum": "CC002",
                        "deviceId": 7,
                        "deviceName": "眼动仪设备",
                        "deviceSn": "DEV002",
                        "operatorId": 7,
                        "operatorName": "operator002",
                        "testType": "FOLLOW_ABILITY",
                        "testSequence": "02",
                        "testDate": "2023-12-01 11:00:00",
                        "duration": 45000,
                        "status": "COMPLETED",
                        "statusDesc": "已完成",
                        "followPathPoints": "[[100, 100], [200, 200], [300, 150]]",
                        "actualGazePoints": "[[105, 98], [198, 205], [295, 148]]",
                        "trackingAccuracy": 92.5,
                        "averageError": 5.2,
                        "reactionTime": 150.5,
                        "smoothnessScore": 88.3,
                        "completionRate": 95,
                        "bezierCurveData": "{}",
                        "followCount": 20,
                        "successfulFollows": 18,
                        "pathLength": 500,
                        "actualPathLength": 485.2,
                        "velocityConsistency": 85.5,
                        "calibrationParams": "\"标准校准\"",
                        "environmentInfo": "\"室内光线充足\"",
                        "notes": "追随能力测试",
                        "imageUrl": null,
                        "createdAt": "2025-06-18 19:53:35",
                        "updatedAt": "2025-06-18 19:53:35"
                      }
                    ],
                    "hasPrevious": false,
                    "hasNext": false
                  },
                  "timestamp": 1750665215092
                }
              }
            },
            "headers": {}
          }
        },
        "security": []
      }
    },
    "/api/movement/follow-ability/9": {
      "get": {
        "summary": "查询追随能力评估详情",
        "deprecated": false,
        "description": "",
        "tags": [],
        "parameters": [
          {
            "name": "Content-Type",
            "in": "header",
            "description": "",
            "required": true,
            "example": "application/json",
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "text/plain": {
              "schema": {
                "type": "string"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    },
                    "data": {
                      "type": "object",
                      "properties": {
                        "id": {
                          "type": "integer"
                        },
                        "recordId": {
                          "type": "integer"
                        },
                        "patientId": {
                          "type": "integer"
                        },
                        "patientName": {
                          "type": "string"
                        },
                        "inpatientNum": {
                          "type": "string"
                        },
                        "caseCardNum": {
                          "type": "string"
                        },
                        "deviceId": {
                          "type": "integer"
                        },
                        "deviceName": {
                          "type": "null"
                        },
                        "deviceSn": {
                          "type": "string"
                        },
                        "operatorId": {
                          "type": "integer"
                        },
                        "operatorName": {
                          "type": "null"
                        },
                        "testType": {
                          "type": "string"
                        },
                        "testSequence": {
                          "type": "string"
                        },
                        "testDate": {
                          "type": "string"
                        },
                        "duration": {
                          "type": "integer"
                        },
                        "status": {
                          "type": "string"
                        },
                        "statusDesc": {
                          "type": "string"
                        },
                        "followPathPoints": {
                          "type": "string"
                        },
                        "actualGazePoints": {
                          "type": "string"
                        },
                        "trackingAccuracy": {
                          "type": "number"
                        },
                        "averageError": {
                          "type": "number"
                        },
                        "reactionTime": {
                          "type": "integer"
                        },
                        "smoothnessScore": {
                          "type": "integer"
                        },
                        "completionRate": {
                          "type": "integer"
                        },
                        "bezierCurveData": {
                          "type": "string"
                        },
                        "followCount": {
                          "type": "integer"
                        },
                        "successfulFollows": {
                          "type": "integer"
                        },
                        "pathLength": {
                          "type": "integer"
                        },
                        "actualPathLength": {
                          "type": "number"
                        },
                        "velocityConsistency": {
                          "type": "integer"
                        },
                        "calibrationParams": {
                          "type": "string"
                        },
                        "environmentInfo": {
                          "type": "string"
                        },
                        "notes": {
                          "type": "string"
                        },
                        "imageUrl": {
                          "type": "string"
                        },
                        "createdAt": {
                          "type": "string"
                        },
                        "updatedAt": {
                          "type": "string"
                        }
                      },
                      "required": [
                        "id",
                        "recordId",
                        "patientId",
                        "patientName",
                        "inpatientNum",
                        "caseCardNum",
                        "deviceId",
                        "deviceName",
                        "deviceSn",
                        "operatorId",
                        "operatorName",
                        "testType",
                        "testSequence",
                        "testDate",
                        "duration",
                        "status",
                        "statusDesc",
                        "followPathPoints",
                        "actualGazePoints",
                        "trackingAccuracy",
                        "averageError",
                        "reactionTime",
                        "smoothnessScore",
                        "completionRate",
                        "bezierCurveData",
                        "followCount",
                        "successfulFollows",
                        "pathLength",
                        "actualPathLength",
                        "velocityConsistency",
                        "calibrationParams",
                        "environmentInfo",
                        "notes",
                        "imageUrl",
                        "createdAt",
                        "updatedAt"
                      ]
                    },
                    "timestamp": {
                      "type": "integer"
                    }
                  },
                  "required": [
                    "code",
                    "message",
                    "data",
                    "timestamp"
                  ]
                },
                "example": {
                  "code": 200,
                  "message": "操作成功",
                  "data": {
                    "id": 9,
                    "recordId": 42,
                    "patientId": 37,
                    "patientName": "韩震",
                    "inpatientNum": "123",
                    "caseCardNum": "123",
                    "deviceId": 12,
                    "deviceName": null,
                    "deviceSn": "DD92443A006",
                    "operatorId": 0,
                    "operatorName": null,
                    "testType": "FOLLOW_ABILITY",
                    "testSequence": "02",
                    "testDate": "2025-06-23 15:00:55",
                    "duration": 60000,
                    "status": "COMPLETED",
                    "statusDesc": "已完成",
                    "followPathPoints": "[[[0.1, 0.1], [0.1, 0.9], [0.2, 0.9], [0.2, 0.1], [0.3, 0.1], [0.3, 0.9], [0.4, 0.9], [0.4, 0.1], [0.5, 0.1], [0.5, 0.9], [0.6, 0.9], [0.6, 0.1], [0.7, 0.1], [0.7, 0.9], [0.8, 0.9], [0.8, 0.1], [0.9, 0.1], [0.9, 0.9]]]",
                    "actualGazePoints": "[{\"x\": 0.0, \"y\": 0.0, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8483347, \"y\": 0.38725543, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8543905, \"y\": 0.32641363, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.97506475, \"y\": 0.061013848, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8847046, \"y\": 0.36617106, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.884881, \"y\": 0.37802693, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.912982, \"y\": 0.5462888, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.913011, \"y\": 0.5838343, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.9135227, \"y\": 0.6235913, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.7317037, \"y\": 0.6869443, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8487195, \"y\": 0.7487929, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.80682737, \"y\": 0.7614798, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.7586285, \"y\": 0.7770674, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.7459054, \"y\": 0.7992808, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.73399234, \"y\": 0.81336784, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.22609985, \"y\": 0.7401909, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.20751214, \"y\": 0.73514766, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5115601, \"y\": 0.7823826, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.52933615, \"y\": 0.81476337, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5125056, \"y\": 0.82361627, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.48738393, \"y\": 0.83061063, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.45756763, \"y\": 0.8251345, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.4086523, \"y\": 0.79066133, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.38842422, \"y\": 0.7851083, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.22713007, \"y\": 0.78394306, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.23464999, \"y\": 0.79819703, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.20235479, \"y\": 0.7986374, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.17868644, \"y\": 0.7966629, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.14995831, \"y\": 0.80483955, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.12202531, \"y\": 0.81163883, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.10456951, \"y\": 0.80669075, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.11697909, \"y\": 0.7729546, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.13885382, \"y\": 0.7358863, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.15465839, \"y\": 0.7281916, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.45917225, \"y\": 0.7253622, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.37577784, \"y\": 0.7297751, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.34290716, \"y\": 0.71836025, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.4336829, \"y\": 0.7078169, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.47324306, \"y\": 0.7016633, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.49224854, \"y\": 0.6814464, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.45907134, \"y\": 0.6929353, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5416047, \"y\": 0.6828013, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5847497, \"y\": 0.67063004, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.64360917, \"y\": 0.6663523, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.6754495, \"y\": 0.67084706, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.68100643, \"y\": 0.69499564, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8656946, \"y\": 0.67046744, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8204622, \"y\": 0.68138087, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8177865, \"y\": 0.6892279, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.83413124, \"y\": 0.6874365, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8475131, \"y\": 0.6730078, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.86592674, \"y\": 0.65428764, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.89283454, \"y\": 0.6474779, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.9075759, \"y\": 0.62912685, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.8881234, \"y\": 0.600021, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.868603, \"y\": 0.590073, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.846166, \"y\": 0.58181024, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.50306445, \"y\": 0.48504493, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5706146, \"y\": 0.50735337, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.6263919, \"y\": 0.54126525, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.63897395, \"y\": 0.5544976, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.61579573, \"y\": 0.5629308, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5979222, \"y\": 0.564575, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055296}, {\"x\": 0.5675854, \"y\": 0.5604127, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.28743494, \"y\": 0.5689056, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.2579973, \"y\": 0.56357807, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.36545685, \"y\": 0.58123976, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.14308384, \"y\": 0.56344163, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.11541194, \"y\": 0.54317665, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.0684766, \"y\": 0.5518033, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.035150442, \"y\": 0.5690327, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.021710753, \"y\": 0.5695876, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.03588687, \"y\": 0.5478033, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.09464323, \"y\": 0.533491, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.11853363, \"y\": 0.53554535, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.10724276, \"y\": 0.57520115, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.08843226, \"y\": 0.54782385, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.09460305, \"y\": 0.5348169, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.16667233, \"y\": 0.5184916, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.20543745, \"y\": 0.5149985, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.22959566, \"y\": 0.51503, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.25019708, \"y\": 0.51624286, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.41646734, \"y\": 0.48450637, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.4341714, \"y\": 0.47816148, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.41115794, \"y\": 0.48155934, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.4520994, \"y\": 0.4842077, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.47505468, \"y\": 0.4920255, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.48839775, \"y\": 0.52619916, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.5044621, \"y\": 0.50923514, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.65701747, \"y\": 0.49892476, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.67206097, \"y\": 0.5001638, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.657578, \"y\": 0.4966777, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.6845211, \"y\": 0.49830547, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.89408886, \"y\": 0.4696183, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.85353345, \"y\": 0.47134736, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8748417, \"y\": 0.4728609, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8868233, \"y\": 0.46389726, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.48615512, \"y\": 0.38933122, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.7189529, \"y\": 0.39757025, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.47956896, \"y\": 0.3655527, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.45255244, \"y\": 0.37292373, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.4928002, \"y\": 0.37434077, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.52057266, \"y\": 0.38944498, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.36910322, \"y\": 0.41699636, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.334962, \"y\": 0.40841326, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.29694614, \"y\": 0.4066515, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.24690408, \"y\": 0.40720892, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.21785887, \"y\": 0.3995138, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.20036942, \"y\": 0.38725197, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.17426346, \"y\": 0.39120948, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.15599464, \"y\": 0.4045003, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.14599809, \"y\": 0.38831565, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.13268434, \"y\": 0.35573444, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.11555706, \"y\": 0.3514263, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.24082851, \"y\": 0.32727864, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.25990388, \"y\": 0.32019222, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.49199376, \"y\": 0.29187423, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.45299804, \"y\": 0.30380157, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.44211796, \"y\": 0.3142341, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.49875668, \"y\": 0.31633398, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.66218895, \"y\": 0.27706417, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.68256664, \"y\": 0.2684501, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.6945721, \"y\": 0.25823334, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.7489535, \"y\": 0.24927455, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.76578236, \"y\": 0.2261644, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.7617149, \"y\": 0.1987255, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8060589, \"y\": 0.24095818, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8161937, \"y\": 0.2688429, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.82346654, \"y\": 0.28274742, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8679287, \"y\": 0.26264817, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.89556026, \"y\": 0.2427405, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.9183298, \"y\": 0.2204759, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.91316885, \"y\": 0.21064056, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8393088, \"y\": 0.19841076, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.79563785, \"y\": 0.19172916, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.77364326, \"y\": 0.1924692, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.3681615, \"y\": 0.21705037, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.34676033, \"y\": 0.21571153, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.43668598, \"y\": 0.20833772, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.46662042, \"y\": 0.21457236, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.49695435, \"y\": 0.22369747, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.5032785, \"y\": 0.23146442, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.35976943, \"y\": 0.18565644, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.333831, \"y\": 0.1756782, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.3248682, \"y\": 0.16895334, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.34566224, \"y\": 0.20849913, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.35016274, \"y\": 0.22457525, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.3361134, \"y\": 0.2410682, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.2571066, \"y\": 0.23643503, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.2210238, \"y\": 0.23418796, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.19771375, \"y\": 0.22203736, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.17362988, \"y\": 0.21329409, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.15196402, \"y\": 0.1857787, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.12788235, \"y\": 0.20321228, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.18444279, \"y\": 0.17594416, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.20809495, \"y\": 0.16786177, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.2358461, \"y\": 0.16139954, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.2881465, \"y\": 0.13590339, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.29956576, \"y\": 0.14461349, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.49832273, \"y\": 0.08449453, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.5326571, \"y\": 0.070148304, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.5816364, \"y\": 0.046093322, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.6068231, \"y\": 0.03782158, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.62065554, \"y\": 0.030054098, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.82787347, \"y\": 0.07224029, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.8463303, \"y\": 0.07954951, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.86511207, \"y\": 0.07796477, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.90700656, \"y\": 0.06730754, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.93003666, \"y\": 0.056729734, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.9457, \"y\": 0.049240407, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.9307888, \"y\": 0.067766234, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.90572786, \"y\": 0.086055405, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.81773126, \"y\": 0.07681636, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.561308, \"y\": 0.029372, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.53830147, \"y\": 0.026875818, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.5633227, \"y\": 0.044779006, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.2516822, \"y\": 0.054630797, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.24634522, \"y\": 0.055892527, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.27286667, \"y\": 0.048236728, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055297}, {\"x\": 0.33323252, \"y\": 0.04194679, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.3578406, \"y\": 0.04289615, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30977547, \"y\": 0.04777413, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.28059608, \"y\": 0.046202008, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.26803103, \"y\": 0.046067074, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.19871709, \"y\": 0.07366523, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.17439587, \"y\": 0.08698571, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.16050133, \"y\": 0.11001137, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.10706836, \"y\": 0.38615358, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.09161748, \"y\": 0.35561633, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.105601594, \"y\": 0.30444926, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.115772165, \"y\": 0.38229334, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.11583937, \"y\": 0.45516875, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.1153247, \"y\": 0.48243308, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.108136274, \"y\": 0.51521146, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.11402277, \"y\": 0.59444743, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.11973018, \"y\": 0.6374733, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.117657386, \"y\": 0.67318106, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.11048015, \"y\": 0.7074717, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.10615428, \"y\": 0.7361834, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.10150453, \"y\": 0.7647793, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.1269197, \"y\": 0.7896668, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.147828, \"y\": 0.80034477, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.16960278, \"y\": 0.7978315, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.19013426, \"y\": 0.751175, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.2118303, \"y\": 0.64612484, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.20224425, \"y\": 0.4355788, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.20932055, \"y\": 0.4091755, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.21357442, \"y\": 0.37998918, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.22056781, \"y\": 0.2932233, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.22383326, \"y\": 0.2526089, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.21959811, \"y\": 0.2206054, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.2160451, \"y\": 0.20260942, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.20515852, \"y\": 0.18466686, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.22302738, \"y\": 0.15793483, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.23364402, \"y\": 0.14212522, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.25480664, \"y\": 0.11682672, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.27174833, \"y\": 0.11841007, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.28811014, \"y\": 0.15808414, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30051234, \"y\": 0.19251572, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.3037628, \"y\": 0.21501178, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30825993, \"y\": 0.28058192, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.31254944, \"y\": 0.3312825, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.3124563, \"y\": 0.36648333, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30614343, \"y\": 0.41056943, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30303073, \"y\": 0.44174758, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30103105, \"y\": 0.4726999, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30112314, \"y\": 0.50668377, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.3132311, \"y\": 0.53600204, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.3061968, \"y\": 0.5763636, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.2950583, \"y\": 0.61507726, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.288805, \"y\": 0.6477458, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.28269458, \"y\": 0.6995622, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.28203452, \"y\": 0.717246, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.29425523, \"y\": 0.7754016, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.30182493, \"y\": 0.8055712, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.39371192, \"y\": 0.24128938, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.41541147, \"y\": 0.5115217, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.44739828, \"y\": 0.27084893, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.4516648, \"y\": 0.24074148, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.48546746, \"y\": 0.13581915, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.38615537, \"y\": 0.3006745, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.41432375, \"y\": 0.25413808, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.41962472, \"y\": 0.22517283, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.40963912, \"y\": 0.21095152, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.42173555, \"y\": 0.16572647, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.4332385, \"y\": 0.114691764, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.43733746, \"y\": 0.09558149, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.4334149, \"y\": 0.086339355, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.44967303, \"y\": 0.09403564, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.46812007, \"y\": 0.10675676, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.49423704, \"y\": 0.26456025, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.50692296, \"y\": 0.2898045, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5008607, \"y\": 0.31705588, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.4880091, \"y\": 0.33723927, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5077031, \"y\": 0.39420274, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5178766, \"y\": 0.42129633, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.52197367, \"y\": 0.44375303, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5093094, \"y\": 0.48910555, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.50226176, \"y\": 0.5176759, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.49031305, \"y\": 0.5517758, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.49557367, \"y\": 0.60008854, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.49778983, \"y\": 0.62490666, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.499313, \"y\": 0.65672565, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5042686, \"y\": 0.6717865, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5089628, \"y\": 0.7114653, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5093038, \"y\": 0.74270827, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5120195, \"y\": 0.766132, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.54189575, \"y\": 0.7754384, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.60131, \"y\": 0.22716904, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6100363, \"y\": 0.20535214, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.5911251, \"y\": 0.5733552, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.60892427, \"y\": 0.5336046, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.65981054, \"y\": 0.25419274, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6371237, \"y\": 0.24694547, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.635407, \"y\": 0.18928902, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6312753, \"y\": 0.14872691, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.61877126, \"y\": 0.12538277, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6205568, \"y\": 0.10544189, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6312133, \"y\": 0.09160424, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6742814, \"y\": 0.09996704, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.69436765, \"y\": 0.106053546, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.7035687, \"y\": 0.123219416, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6948873, \"y\": 0.1650916, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6907413, \"y\": 0.18684597, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.68949866, \"y\": 0.22408971, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.70242506, \"y\": 0.24898043, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.70557225, \"y\": 0.27212194, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.693953, \"y\": 0.34061176, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.6800543, \"y\": 0.3852393, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.67472005, \"y\": 0.4139169, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.66996056, \"y\": 0.442011, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.70584893, \"y\": 0.4936035, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.70908374, \"y\": 0.5165461, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.7126081, \"y\": 0.54515785, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055298}, {\"x\": 0.7306502, \"y\": 0.5379789, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.77344626, \"y\": 0.387164, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.7975075, \"y\": 0.4090653, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.80815196, \"y\": 0.4199717, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.9011145, \"y\": 0.09041342, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.91249067, \"y\": 0.06573297, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.88508016, \"y\": 0.105203494, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.86871123, \"y\": 0.12717341, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.85099, \"y\": 0.14197676, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.83657324, \"y\": 0.15075523, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.85261, \"y\": 0.12370044, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.86607796, \"y\": 0.10322084, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.87989354, \"y\": 0.09779955, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.892967, \"y\": 0.10987817, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.89890426, \"y\": 0.17330757, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.9015206, \"y\": 0.21814781, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.9046446, \"y\": 0.24866273, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.90363777, \"y\": 0.27455235, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.9175477, \"y\": 0.5254818, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.91580456, \"y\": 0.5611804, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.90388983, \"y\": 0.58578724, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.8823229, \"y\": 0.59257156, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}, {\"x\": 0.8851623, \"y\": 0.62267846, \"distance\": 0.0, \"duration\": 0, \"timestamp\": 1750662055299}]",
                    "trackingAccuracy": 11.43,
                    "averageError": 0.1963,
                    "reactionTime": 0,
                    "smoothnessScore": 0,
                    "completionRate": 100,
                    "bezierCurveData": "[{\"endPoint\": [0.2, 0.1], \"startPoint\": [0.1, 0.1], \"controlPoint1\": [0.1, 0.9], \"controlPoint2\": [0.2, 0.9]}]",
                    "followCount": 1,
                    "successfulFollows": 0,
                    "pathLength": 8,
                    "actualPathLength": 21.36,
                    "velocityConsistency": 0,
                    "calibrationParams": "\"标准校准\"",
                    "environmentInfo": "\"室内光线充足\"",
                    "notes": "追随能力测试",
                    "imageUrl": "http://localhost:8080/api/files/20250623_150057_4efaa3f7.png",
                    "createdAt": "2025-06-23 15:01:08",
                    "updatedAt": "2025-06-23 15:01:08"
                  },
                  "timestamp": 1750665469987
                }
              }
            },
            "headers": {}
          }
        },
        "security": []
      }
    },
    "/api/movement/error-evaluation/bezier": {
      "post": {
        "summary": "贝塞尔曲线误差评估接口测试",
        "deprecated": false,
        "description": "",
        "tags": [],
        "parameters": [
          {
            "name": "Content-Type",
            "in": "header",
            "description": "",
            "required": true,
            "example": "application/json",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "X-Device-Sn",
            "in": "header",
            "description": "",
            "required": true,
            "example": "TEST_DEVICE_001",
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "targetPoints": {
                    "type": "array",
                    "items": {
                      "type": "object",
                      "properties": {
                        "x": {
                          "type": "number"
                        },
                        "y": {
                          "type": "number"
                        },
                        "timestamp": {
                          "type": "integer"
                        },
                        "isValid": {
                          "type": "boolean"
                        }
                      },
                      "required": [
                        "x",
                        "y",
                        "timestamp",
                        "isValid"
                      ]
                    }
                  },
                  "actualGazePoints": {
                    "type": "array",
                    "items": {
                      "type": "object",
                      "properties": {
                        "x": {
                          "type": "number"
                        },
                        "y": {
                          "type": "number"
                        },
                        "timestamp": {
                          "type": "integer"
                        },
                        "isValid": {
                          "type": "boolean"
                        }
                      },
                      "required": [
                        "x",
                        "y",
                        "timestamp",
                        "isValid"
                      ]
                    }
                  },
                  "screenWidth": {
                    "type": "integer"
                  },
                  "screenHeight": {
                    "type": "integer"
                  },
                  "enableGridStatistics": {
                    "type": "boolean"
                  }
                },
                "required": [
                  "targetPoints",
                  "actualGazePoints",
                  "screenWidth",
                  "screenHeight",
                  "enableGridStatistics"
                ]
              },
              "example": {
                "targetPoints": [
                  {
                    "x": 100.5,
                    "y": 200.3,
                    "timestamp": 1640995200000,
                    "isValid": true
                  },
                  {
                    "x": 300.8,
                    "y": 400.2,
                    "timestamp": 1640995201000,
                    "isValid": true
                  },
                  {
                    "x": 500.1,
                    "y": 300.7,
                    "timestamp": 1640995202000,
                    "isValid": true
                  },
                  {
                    "x": 700.4,
                    "y": 500.9,
                    "timestamp": 1640995203000,
                    "isValid": true
                  }
                ],
                "actualGazePoints": [
                  {
                    "x": 102.3,
                    "y": 198.7,
                    "timestamp": 1640995200100,
                    "isValid": true
                  },
                  {
                    "x": 298.5,
                    "y": 402.1,
                    "timestamp": 1640995201100,
                    "isValid": true
                  },
                  {
                    "x": 503.2,
                    "y": 299.4,
                    "timestamp": 1640995202100,
                    "isValid": true
                  },
                  {
                    "x": 695.8,
                    "y": 498.6,
                    "timestamp": 1640995203100,
                    "isValid": true
                  }
                ],
                "screenWidth": 1920,
                "screenHeight": 1080,
                "enableGridStatistics": true
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    },
                    "data": {
                      "type": "object",
                      "properties": {
                        "averageError": {
                          "type": "number"
                        },
                        "maxError": {
                          "type": "number"
                        },
                        "minError": {
                          "type": "number"
                        },
                        "errorStandardDeviation": {
                          "type": "number"
                        },
                        "rmsError": {
                          "type": "number"
                        },
                        "trackingAccuracy": {
                          "type": "integer"
                        },
                        "pathDeviation": {
                          "type": "number"
                        },
                        "smoothnessScore": {
                          "type": "number"
                        },
                        "pointErrors": {
                          "type": "array",
                          "items": {
                            "type": "object",
                            "properties": {
                              "index": {
                                "type": "integer"
                              },
                              "targetX": {
                                "type": "number"
                              },
                              "targetY": {
                                "type": "number"
                              },
                              "actualX": {
                                "type": "number"
                              },
                              "actualY": {
                                "type": "number"
                              },
                              "errorDistance": {
                                "type": "number"
                              },
                              "errorX": {
                                "type": "number"
                              },
                              "errorY": {
                                "type": "number"
                              },
                              "timestamp": {
                                "type": "integer"
                              }
                            },
                            "required": [
                              "index",
                              "targetX",
                              "targetY",
                              "actualX",
                              "actualY",
                              "errorDistance",
                              "errorX",
                              "errorY",
                              "timestamp"
                            ]
                          }
                        },
                        "gridStatistics": {
                          "type": "object",
                          "properties": {
                            "gridCounts": {
                              "type": "array",
                              "items": {
                                "type": "integer"
                              }
                            },
                            "gridPercentages": {
                              "type": "array",
                              "items": {
                                "type": "integer"
                              }
                            },
                            "centerCount": {
                              "type": "integer"
                            },
                            "centerPercentage": {
                              "type": "integer"
                            },
                            "edgeCount": {
                              "type": "integer"
                            },
                            "edgePercentage": {
                              "type": "integer"
                            },
                            "totalPoints": {
                              "type": "integer"
                            }
                          },
                          "required": [
                            "gridCounts",
                            "gridPercentages",
                            "centerCount",
                            "centerPercentage",
                            "edgeCount",
                            "edgePercentage",
                            "totalPoints"
                          ]
                        }
                      },
                      "required": [
                        "averageError",
                        "maxError",
                        "minError",
                        "errorStandardDeviation",
                        "rmsError",
                        "trackingAccuracy",
                        "pathDeviation",
                        "smoothnessScore",
                        "pointErrors",
                        "gridStatistics"
                      ]
                    },
                    "timestamp": {
                      "type": "integer"
                    }
                  },
                  "required": [
                    "code",
                    "message",
                    "data",
                    "timestamp"
                  ]
                },
                "example": {
                  "code": 200,
                  "message": "贝塞尔曲线误差评估完成",
                  "data": {
                    "averageError": 3.474,
                    "maxError": 5.143,
                    "minError": 2.4083,
                    "errorStandardDeviation": 1.0216,
                    "rmsError": 3.6211,
                    "trackingAccuracy": 100,
                    "pathDeviation": 0.0347,
                    "smoothnessScore": 99.09,
                    "pointErrors": [
                      {
                        "index": 0,
                        "targetX": 100.5,
                        "targetY": 200.3,
                        "actualX": 102.3,
                        "actualY": 198.7,
                        "errorDistance": 2.4083,
                        "errorX": 1.8,
                        "errorY": -1.6,
                        "timestamp": 1640995200100
                      },
                      {
                        "index": 1,
                        "targetX": 300.8,
                        "targetY": 400.2,
                        "actualX": 298.5,
                        "actualY": 402.1,
                        "errorDistance": 2.9833,
                        "errorX": -2.3,
                        "errorY": 1.9,
                        "timestamp": 1640995201100
                      },
                      {
                        "index": 2,
                        "targetX": 500.1,
                        "targetY": 300.7,
                        "actualX": 503.2,
                        "actualY": 299.4,
                        "errorDistance": 3.3615,
                        "errorX": 3.1,
                        "errorY": -1.3,
                        "timestamp": 1640995202100
                      },
                      {
                        "index": 3,
                        "targetX": 700.4,
                        "targetY": 500.9,
                        "actualX": 695.8,
                        "actualY": 498.6,
                        "errorDistance": 5.143,
                        "errorX": -4.6,
                        "errorY": -2.3,
                        "timestamp": 1640995203100
                      }
                    ],
                    "gridStatistics": {
                      "gridCounts": [
                        2,
                        0,
                        0,
                        1,
                        1,
                        0,
                        0,
                        0,
                        0
                      ],
                      "gridPercentages": [
                        50,
                        0,
                        0,
                        25,
                        25,
                        0,
                        0,
                        0,
                        0
                      ],
                      "centerCount": 1,
                      "centerPercentage": 25,
                      "edgeCount": 3,
                      "edgePercentage": 75,
                      "totalPoints": 4
                    }
                  },
                  "timestamp": 1750665731998
                }
              }
            },
            "headers": {}
          }
        },
        "security": []
      }
    }
  },