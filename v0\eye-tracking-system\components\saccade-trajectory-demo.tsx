"use client"

import React from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Target, Home, Eye, Zap } from "lucide-react"
import { useRouter } from "next/navigation"

export default function SaccadeTrajectoryDemo() {
  const router = useRouter()

  // 模拟扫视轨迹记录数据
  const mockRecord = {
    imageUrl: "https://via.placeholder.com/800x600/f0f0f0/333333?text=扫视轨迹图",
    patientName: "演示患者",
    testSequence: "DEMO-001"
  }

  // 模拟扫视点数据
  const mockSaccadePoints = [
    {
      index: 1,
      x: 0.2,
      y: 0.3,
      accuracy: 85.5,
      duration: 120,
      velocity: 450.2,
      saccadeType: "NORMAL_SACCADE",
      saccadeQuality: "GOOD",
      isValidSaccade: true,
      isOnTarget: true,
      distance: 5.2,
      errorType: "NONE",
      targetIndex: 1,
      distanceToTarget: 2.1
    },
    {
      index: 2,
      x: 0.7,
      y: 0.4,
      accuracy: 92.1,
      duration: 95,
      velocity: 520.8,
      saccadeType: "LARGE_SACCADE",
      saccadeQuality: "GOOD",
      isValidSaccade: true,
      isOnTarget: true,
      distance: 8.7,
      errorType: "NONE",
      targetIndex: 2,
      distanceToTarget: 1.5
    },
    {
      index: 3,
      x: 0.3,
      y: 0.8,
      accuracy: 78.3,
      duration: 140,
      velocity: 380.5,
      saccadeType: "NORMAL_SACCADE",
      saccadeQuality: "FAIR",
      isValidSaccade: true,
      isOnTarget: false,
      distance: 6.1,
      errorType: "UNDERSHOOT",
      targetIndex: 3,
      distanceToTarget: 3.2
    },
    {
      index: 4,
      x: 0.8,
      y: 0.2,
      accuracy: 88.7,
      duration: 110,
      velocity: 475.3,
      saccadeType: "NORMAL_SACCADE",
      saccadeQuality: "GOOD",
      isValidSaccade: true,
      isOnTarget: true,
      distance: 7.3,
      errorType: "NONE",
      targetIndex: 4,
      distanceToTarget: 1.8
    }
  ]

  // 获取扫视质量颜色
  const getSaccadeQualityColor = (quality: string) => {
    switch (quality) {
      case 'GOOD':
        return 'text-green-600'
      case 'FAIR':
        return 'text-yellow-600'
      case 'POOR':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  // 获取扫视类型标签
  const getSaccadeTypeLabel = (type: string) => {
    switch (type) {
      case 'INITIAL':
        return '初始'
      case 'NORMAL_SACCADE':
        return '正常扫视'
      case 'LARGE_SACCADE':
        return '大幅扫视'
      case 'FINAL':
        return '结束'
      default:
        return type
    }
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <Target className="h-8 w-8 text-purple-600" />
            扫视轨迹可视化演示
          </h1>
          <p className="text-gray-600 mt-1">展示扫视能力测试的轨迹图像和数据分析功能</p>
        </div>
        <Button variant="outline" onClick={() => router.push('/')} className="flex items-center gap-2">
          <Home className="h-4 w-4" />
          返回主页
        </Button>
      </div>

      {/* 功能说明 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>功能说明</CardTitle>
          <CardDescription>
            扫视轨迹可视化功能展示眼球扫视运动的轨迹图像和详细数据分析
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="p-3 bg-purple-50 rounded-lg">
              <h4 className="font-semibold text-purple-800 mb-2">轨迹图像</h4>
              <p className="text-purple-700">显示扫视运动的完整轨迹图像，直观展示眼球运动路径</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">统计分析</h4>
              <p className="text-blue-700">计算扫视点数、准确度、速度等关键指标</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">详细数据</h4>
              <p className="text-green-700">展示每个扫视点的坐标、质量、类型等详细信息</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 扫视轨迹演示 */}
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              扫视轨迹可视化
            </CardTitle>
            <CardDescription>
              显示眼球扫视运动的轨迹和质量分析
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="border rounded-lg overflow-hidden">
                <img
                  src={mockRecord.imageUrl}
                  alt="扫视轨迹图"
                  className="w-full h-auto"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none'
                    e.currentTarget.nextElementSibling?.classList.remove('hidden')
                  }}
                />
                <div className="hidden p-8 text-center text-gray-500">
                  <Eye className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                  <p>轨迹图像加载失败</p>
                </div>
              </div>

              {/* 轨迹数据统计 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">轨迹统计</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">扫视点数</span>
                      <span className="font-medium">{mockSaccadePoints.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">有效扫视</span>
                      <span className="font-medium text-green-600">
                        {mockSaccadePoints.filter(p => p.isValidSaccade).length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">平均准确度</span>
                      <span className="font-medium">
                        {(mockSaccadePoints.reduce((sum, p) => sum + p.accuracy, 0) / mockSaccadePoints.length).toFixed(1)}%
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">质量分析</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">平均速度</span>
                      <span className="font-medium">
                        {(mockSaccadePoints.reduce((sum, p) => sum + p.velocity, 0) / mockSaccadePoints.length).toFixed(1)} °/s
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">平均持续时间</span>
                      <span className="font-medium">
                        {(mockSaccadePoints.reduce((sum, p) => sum + p.duration, 0) / mockSaccadePoints.length).toFixed(0)} ms
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">目标命中率</span>
                      <span className="font-medium text-green-600">
                        {((mockSaccadePoints.filter(p => p.isOnTarget).length / mockSaccadePoints.length) * 100).toFixed(1)}%
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 扫视轨迹数据详情 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5" />
              扫视轨迹数据详情
            </CardTitle>
            <CardDescription>
              共记录 {mockSaccadePoints.length} 个扫视点
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {mockSaccadePoints.map((point, index) => (
                <div key={index} className="border rounded-lg p-3 bg-gray-50">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">扫视点 {point.index}</span>
                    <Badge variant={point.isValidSaccade ? "default" : "secondary"}>
                      {point.isValidSaccade ? "有效" : "无效"}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-gray-600">坐标:</span>
                      <span className="ml-1">({point.x.toFixed(3)}, {point.y.toFixed(3)})</span>
                    </div>
                    <div>
                      <span className="text-gray-600">准确度:</span>
                      <span className="ml-1">{point.accuracy.toFixed(1)}%</span>
                    </div>
                    <div>
                      <span className="text-gray-600">持续时间:</span>
                      <span className="ml-1">{point.duration}ms</span>
                    </div>
                    <div>
                      <span className="text-gray-600">速度:</span>
                      <span className="ml-1">{point.velocity.toFixed(1)}°/s</span>
                    </div>
                    <div>
                      <span className="text-gray-600">类型:</span>
                      <span className="ml-1">{getSaccadeTypeLabel(point.saccadeType)}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">质量:</span>
                      <span className={`ml-1 ${getSaccadeQualityColor(point.saccadeQuality)}`}>
                        {point.saccadeQuality}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 使用说明 */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">在扫视能力详情页面中查看</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
                <li>进入扫视能力测试列表</li>
                <li>点击任意测试记录查看详情</li>
                <li>切换到"扫视轨迹"标签页</li>
                <li>查看轨迹图像和数据分析</li>
              </ol>
            </div>
            
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">数据来源</h4>
              <p className="text-sm text-blue-700">
                轨迹图像通过 <code className="bg-blue-100 px-1 rounded">record.imageUrl</code> 字段获取，
                扫视点数据通过解析 <code className="bg-blue-100 px-1 rounded">record.gazeTrajectoryJson</code> 字段获取。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
