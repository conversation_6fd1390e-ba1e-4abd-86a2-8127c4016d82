"use client"

import React, { useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Grid3X3, Eye, BarChart3 } from "lucide-react"

interface GazePoint {
  x: number
  y: number
  distance?: number
  duration?: number
  timestamp?: number
}

interface GazeDistributionGridProps {
  actualGazePoints: string // JSON字符串格式的实际注视点
  screenWidth?: number
  screenHeight?: number
}

export default function GazeDistributionGrid({ 
  actualGazePoints, 
  screenWidth = 1920, 
  screenHeight = 1080 
}: GazeDistributionGridProps) {
  
  // 解析注视点数据
  const gazePoints = useMemo(() => {
    try {
      const parsed = JSON.parse(actualGazePoints)
      if (Array.isArray(parsed)) {
        return parsed.map((point: any) => ({
          x: typeof point.x === 'number' ? point.x : 0,
          y: typeof point.y === 'number' ? point.y : 0,
          distance: point.distance || 0,
          duration: point.duration || 0,
          timestamp: point.timestamp || 0
        })) as GazePoint[]
      }
      return []
    } catch (error) {
      console.error('解析注视点数据失败:', error)
      return []
    }
  }, [actualGazePoints])

  // 计算九宫格分布
  const gridDistribution = useMemo(() => {
    if (gazePoints.length === 0) {
      return {
        gridCounts: Array(9).fill(0),
        gridPercentages: Array(9).fill(0),
        centerCount: 0,
        centerPercentage: 0,
        edgeCount: 0,
        edgePercentage: 0,
        totalPoints: 0
      }
    }

    const gridCounts = Array(9).fill(0)
    
    gazePoints.forEach(point => {
      // 将归一化坐标转换为网格索引
      const gridX = Math.floor(point.x * 3)
      const gridY = Math.floor(point.y * 3)
      
      // 确保索引在有效范围内
      const clampedX = Math.max(0, Math.min(2, gridX))
      const clampedY = Math.max(0, Math.min(2, gridY))
      
      const gridIndex = clampedY * 3 + clampedX
      gridCounts[gridIndex]++
    })

    const totalPoints = gazePoints.length
    const gridPercentages = gridCounts.map(count => (count / totalPoints) * 100)
    
    // 计算中心区域（索引4）和边缘区域的统计
    const centerCount = gridCounts[4] // 中心格子
    const centerPercentage = (centerCount / totalPoints) * 100
    const edgeCount = totalPoints - centerCount
    const edgePercentage = (edgeCount / totalPoints) * 100

    return {
      gridCounts,
      gridPercentages,
      centerCount,
      centerPercentage,
      edgeCount,
      edgePercentage,
      totalPoints
    }
  }, [gazePoints])

  // 获取网格区域名称
  const getGridName = (index: number) => {
    const names = [
      '左上', '上中', '右上',
      '左中', '中心', '右中',
      '左下', '下中', '右下'
    ]
    return names[index]
  }

  if (gazePoints.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Grid3X3 className="h-5 w-5" />
            注视分布分析
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 p-4 bg-gray-50 rounded-lg">
            <Eye className="h-4 w-4 text-gray-500" />
            <span className="text-gray-600">暂无注视点数据</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Grid3X3 className="h-5 w-5" />
          注视分布分析
        </CardTitle>
        <CardDescription>
          九宫格区域分布热力图，颜色深度表示注视点密度
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 九宫格热力图 */}
        <div className="flex justify-center">
          <div className="w-full max-w-sm">
            <div className="grid grid-cols-3 gap-1 aspect-square">
              {gridDistribution.gridCounts.map((count, index) => {
                const percentage = gridDistribution.gridPercentages[index]
                const intensity = Math.min(percentage / 30, 1) // 最大30%为满色
                const bgColor = count > 0 
                  ? `rgba(59, 130, 246, ${0.2 + intensity * 0.8})` 
                  : 'rgba(229, 231, 235, 0.3)'
                
                return (
                  <div
                    key={index}
                    className="aspect-square border border-gray-300 rounded flex flex-col items-center justify-center text-xs font-medium relative group cursor-pointer"
                    style={{ backgroundColor: bgColor }}
                    title={`${getGridName(index)}: ${count}个点 (${percentage.toFixed(1)}%)`}
                  >
                    <div className="text-lg font-bold">{count}</div>
                    <div className="text-xs text-gray-600">{percentage.toFixed(1)}%</div>
                    
                    {/* 悬停提示 */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                      {getGridName(index)}区域
                    </div>
                  </div>
                )
              })}
            </div>
            
            {/* 图例 */}
            <div className="mt-3 text-center text-sm text-gray-600">
              <div className="flex justify-center items-center gap-4">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-200 rounded"></div>
                  <span>低密度</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-600 rounded"></div>
                  <span>高密度</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {gridDistribution.totalPoints}
            </div>
            <div className="text-xs text-gray-600">总注视点数</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {gridDistribution.centerPercentage.toFixed(1)}%
            </div>
            <div className="text-xs text-gray-600">中心区域占比</div>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">
              {gridDistribution.edgePercentage.toFixed(1)}%
            </div>
            <div className="text-xs text-gray-600">边缘区域占比</div>
          </div>
        </div>

        {/* 分布特征分析 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-semibold mb-2 flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              分布特征
            </h4>
            <ul className="space-y-1 text-gray-600">
              <li>• 最高集中区域: {getGridName(gridDistribution.gridCounts.indexOf(Math.max(...gridDistribution.gridCounts)))}</li>
              <li>• 分布均匀度: {gridDistribution.gridCounts.filter(c => c > 0).length}/9 区域有分布</li>
              <li>• 中心偏好: {gridDistribution.centerPercentage > 50 ? '是' : '否'}</li>
              <li>• 边缘偏好: {gridDistribution.edgePercentage > 50 ? '是' : '否'}</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-2">视线模式分析</h4>
            <ul className="space-y-1 text-gray-600">
              {gridDistribution.centerPercentage > 60 && (
                <li className="text-green-600">✓ 中心集中型 - 注意力高度集中</li>
              )}
              {gridDistribution.edgePercentage > 70 && (
                <li className="text-blue-600">ℹ 边缘扫描型 - 符合边缘优先模式</li>
              )}
              {gridDistribution.gridCounts.filter(c => c > 0).length >= 7 && (
                <li className="text-purple-600">◆ 均匀分布型 - 全面扫描模式</li>
              )}
              {Math.max(...gridDistribution.gridPercentages) > 40 && (
                <li className="text-orange-600">⚠ 局部偏重 - 某区域过度关注</li>
              )}
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
