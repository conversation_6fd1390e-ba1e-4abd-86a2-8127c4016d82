"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import apiService from "@/lib/api"

export default function TestSaccadePage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const testSaccadeAbilityList = async () => {
    setLoading(true)
    setError(null)
    setResult(null)
    
    try {
      const response = await apiService.getSaccadeAbilityList({
        current: 1,
        size: 10
      })
      setResult(response)
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败')
    } finally {
      setLoading(false)
    }
  }

  const testSaccadeAbilityDetail = async () => {
    setLoading(true)
    setError(null)
    setResult(null)
    
    try {
      const response = await apiService.getSaccadeAbilityDetail(1)
      setResult(response)
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <Card>
        <CardHeader>
          <CardTitle>扫视能力API测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={testSaccadeAbilityList} disabled={loading}>
              测试扫视能力列表API
            </Button>
            <Button onClick={testSaccadeAbilityDetail} disabled={loading}>
              测试扫视能力详情API
            </Button>
          </div>
          
          {loading && (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="ml-2">请求中...</span>
            </div>
          )}
          
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600">错误: {error}</p>
            </div>
          )}
          
          {result && (
            <div className="p-4 bg-gray-50 border rounded-md">
              <h3 className="font-medium mb-2">API响应:</h3>
              <pre className="text-sm overflow-x-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
