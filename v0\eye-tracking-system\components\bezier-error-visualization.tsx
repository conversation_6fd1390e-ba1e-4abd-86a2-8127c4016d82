"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Target,
  Activity,
  BarChart3,
  Grid3X3,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  FileText
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import apiService, { 
  BezierErrorEvaluationRequest, 
  BezierErrorEvaluationResponse,
  BezierPoint
} from "@/lib/api"

interface BezierErrorVisualizationProps {
  followPathPoints: string // JSON字符串格式的目标路径点
  actualGazePoints: string // JSON字符串格式的实际注视点
  screenWidth?: number
  screenHeight?: number
}

export default function BezierErrorVisualization({ 
  followPathPoints, 
  actualGazePoints, 
  screenWidth = 1920, 
  screenHeight = 1080 
}: BezierErrorVisualizationProps) {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<BezierErrorEvaluationResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("overview")

  const { toast } = useToast()

  // 解析路径点数据
  const parsePathPoints = (jsonString: string): BezierPoint[] => {
    try {
      const parsed = JSON.parse(jsonString)
      
      // 处理不同的数据格式
      if (Array.isArray(parsed)) {
        // 如果是嵌套数组格式 [[[x1, y1], [x2, y2], ...]]
        if (parsed.length > 0 && Array.isArray(parsed[0]) && Array.isArray(parsed[0][0])) {
          return parsed[0].map((point: number[], index: number) => ({
            x: point[0] * screenWidth, // 假设原始数据是归一化的
            y: point[1] * screenHeight,
            timestamp: Date.now() + index * 100,
            isValid: true
          }))
        }
        // 如果是简单数组格式 [[x1, y1], [x2, y2], ...]
        else if (parsed.length > 0 && Array.isArray(parsed[0])) {
          return parsed.map((point: number[], index: number) => ({
            x: point[0] * screenWidth,
            y: point[1] * screenHeight,
            timestamp: Date.now() + index * 100,
            isValid: true
          }))
        }
        // 如果已经是对象格式
        else if (parsed.length > 0 && typeof parsed[0] === 'object') {
          return parsed.map((point: any, index: number) => ({
            x: point.x || point[0] || 0,
            y: point.y || point[1] || 0,
            timestamp: point.timestamp || Date.now() + index * 100,
            isValid: point.isValid !== undefined ? point.isValid : true
          }))
        }
      }
      
      return []
    } catch (err) {
      console.error('解析路径点数据失败:', err)
      return []
    }
  }

  // 执行贝塞尔曲线误差评估
  const runEvaluation = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      // 解析目标点和实际注视点
      const targetPoints = parsePathPoints(followPathPoints)
      const actualPoints = parsePathPoints(actualGazePoints)

      if (targetPoints.length === 0) {
        throw new Error('无法解析目标路径点数据')
      }
      if (actualPoints.length === 0) {
        throw new Error('无法解析实际注视点数据')
      }

      // 确保两个数组长度一致，取较短的长度
      const minLength = Math.min(targetPoints.length, actualPoints.length)
      const alignedTargetPoints = targetPoints.slice(0, minLength)
      const alignedActualPoints = actualPoints.slice(0, minLength)

      const requestData: BezierErrorEvaluationRequest = {
        targetPoints: alignedTargetPoints,
        actualGazePoints: alignedActualPoints,
        screenWidth,
        screenHeight,
        enableGridStatistics: true
      }

      const response = await apiService.evaluateBezierError(requestData)
      setResult(response.data)
      
      toast({
        title: "评估完成",
        description: "贝塞尔曲线误差评估已成功完成",
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '评估失败'
      setError(errorMessage)
      toast({
        title: "评估失败",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 组件加载时自动执行评估
  useEffect(() => {
    if (followPathPoints && actualGazePoints) {
      runEvaluation()
    }
  }, [followPathPoints, actualGazePoints])

  if (!followPathPoints || !actualGazePoints) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            贝塞尔曲线误差评估
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 p-4 bg-gray-50 rounded-lg">
            <AlertCircle className="h-4 w-4 text-gray-500" />
            <span className="text-gray-600">缺少路径数据，无法进行误差评估</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              贝塞尔曲线误差评估
            </CardTitle>
            <CardDescription>
              分析目标轨迹与实际注视轨迹的误差和分布特征
            </CardDescription>
          </div>
          <Button 
            onClick={runEvaluation} 
            disabled={loading}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            {loading ? "评估中..." : "重新评估"}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg mb-4">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        )}

        {loading && (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5 animate-spin" />
              <span>正在进行误差评估...</span>
            </div>
          </div>
        )}

        {result && (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                概览
              </TabsTrigger>
              <TabsTrigger value="visualization" className="flex items-center gap-2">
                <Target className="w-4 h-4" />
                轨迹对比
              </TabsTrigger>
              <TabsTrigger value="grid" className="flex items-center gap-2">
                <Grid3X3 className="w-4 h-4" />
                分布分析
              </TabsTrigger>
            </TabsList>

            {/* 概览标签页 */}
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {result.averageError.toFixed(2)}
                  </div>
                  <div className="text-xs text-gray-600">平均误差 (px)</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {result.maxError.toFixed(2)}
                  </div>
                  <div className="text-xs text-gray-600">最大误差 (px)</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {result.trackingAccuracy.toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-600">跟踪精度</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {result.smoothnessScore.toFixed(1)}
                  </div>
                  <div className="text-xs text-gray-600">平滑度评分</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-semibold text-sm">误差统计</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">最小误差</span>
                      <span>{result.minError.toFixed(4)} px</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">标准差</span>
                      <span>{result.errorStandardDeviation.toFixed(4)} px</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">RMS误差</span>
                      <span>{result.rmsError.toFixed(4)} px</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">路径偏差</span>
                      <span>{result.pathDeviation.toFixed(4)}</span>
                    </div>
                  </div>
                </div>

                {result.gridStatistics && (
                  <div className="space-y-2">
                    <h4 className="font-semibold text-sm">分布特征</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">中心区域占比</span>
                        <span>{result.gridStatistics.centerPercentage.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">边缘区域占比</span>
                        <span>{result.gridStatistics.edgePercentage.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">总点数</span>
                        <span>{result.gridStatistics.totalPoints}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            {/* 轨迹对比标签页 */}
            <TabsContent value="visualization" className="space-y-4">
              <div className="w-full h-80 border rounded-lg bg-gray-50 relative overflow-hidden">
                <svg width="100%" height="100%" viewBox="0 0 800 600" className="absolute inset-0">
                  {/* 目标点 */}
                  {result.pointErrors.map((point, index) => (
                    <g key={`target-${index}`}>
                      <circle
                        cx={point.targetX * 800 / screenWidth}
                        cy={point.targetY * 600 / screenHeight}
                        r="6"
                        fill="none"
                        stroke="red"
                        strokeWidth="2"
                        opacity="0.7"
                      />
                      <text
                        x={point.targetX * 800 / screenWidth + 8}
                        y={point.targetY * 600 / screenHeight + 4}
                        className="text-xs fill-red-600"
                      >
                        T{index}
                      </text>
                    </g>
                  ))}

                  {/* 实际注视点 */}
                  {result.pointErrors.map((point, index) => (
                    <g key={`actual-${index}`}>
                      <circle
                        cx={point.actualX * 800 / screenWidth}
                        cy={point.actualY * 600 / screenHeight}
                        r="3"
                        fill="blue"
                        opacity="0.8"
                      />
                      <text
                        x={point.actualX * 800 / screenWidth + 6}
                        y={point.actualY * 600 / screenHeight - 6}
                        className="text-xs fill-blue-600"
                      >
                        A{index}
                      </text>
                    </g>
                  ))}

                  {/* 误差连线 */}
                  {result.pointErrors.map((point, index) => (
                    <line
                      key={`error-line-${index}`}
                      x1={point.targetX * 800 / screenWidth}
                      y1={point.targetY * 600 / screenHeight}
                      x2={point.actualX * 800 / screenWidth}
                      y2={point.actualY * 600 / screenHeight}
                      stroke="orange"
                      strokeWidth="1"
                      strokeDasharray="2,2"
                      opacity="0.6"
                    />
                  ))}

                  {/* 目标轨迹连线 */}
                  {result.pointErrors.slice(0, -1).map((point, index) => (
                    <line
                      key={`target-path-${index}`}
                      x1={point.targetX * 800 / screenWidth}
                      y1={point.targetY * 600 / screenHeight}
                      x2={result.pointErrors[index + 1].targetX * 800 / screenWidth}
                      y2={result.pointErrors[index + 1].targetY * 600 / screenHeight}
                      stroke="red"
                      strokeWidth="2"
                      opacity="0.4"
                    />
                  ))}

                  {/* 实际轨迹连线 */}
                  {result.pointErrors.slice(0, -1).map((point, index) => (
                    <line
                      key={`actual-path-${index}`}
                      x1={point.actualX * 800 / screenWidth}
                      y1={point.actualY * 600 / screenHeight}
                      x2={result.pointErrors[index + 1].actualX * 800 / screenWidth}
                      y2={result.pointErrors[index + 1].actualY * 600 / screenHeight}
                      stroke="blue"
                      strokeWidth="2"
                      opacity="0.6"
                    />
                  ))}
                </svg>
              </div>
              <div className="text-sm text-gray-600 text-center">
                红色圆圈：目标点 | 蓝色点：实际注视点 | 橙色虚线：误差方向
              </div>
            </TabsContent>

            {/* 分布分析标签页 */}
            <TabsContent value="grid" className="space-y-4">
              {result.gridStatistics && (
                <>
                  <div className="w-full max-w-sm mx-auto">
                    <div className="grid grid-cols-3 gap-1 aspect-square">
                      {result.gridStatistics.gridCounts.map((count, index) => {
                        const percentage = result.gridStatistics.gridPercentages[index]
                        const intensity = Math.min(percentage / 50, 1) // 最大50%为满色
                        const bgColor = count > 0
                          ? `rgba(59, 130, 246, ${0.2 + intensity * 0.8})`
                          : 'rgba(229, 231, 235, 0.5)'

                        return (
                          <div
                            key={index}
                            className="aspect-square border border-gray-300 rounded flex flex-col items-center justify-center text-xs font-medium"
                            style={{ backgroundColor: bgColor }}
                          >
                            <div className="text-lg font-bold">{count}</div>
                            <div className="text-xs text-gray-600">{percentage.toFixed(1)}%</div>
                          </div>
                        )
                      })}
                    </div>
                    <div className="mt-3 text-center text-sm text-gray-600">
                      <div className="flex justify-center items-center gap-4">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-blue-200 rounded"></div>
                          <span>低密度</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-blue-600 rounded"></div>
                          <span>高密度</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <h4 className="font-semibold mb-2">分布特征</h4>
                      <ul className="space-y-1 text-gray-600">
                        <li>• 最高集中区域: 区域{result.gridStatistics.gridCounts.indexOf(Math.max(...result.gridStatistics.gridCounts))}</li>
                        <li>• 分布均匀度: {result.gridStatistics.gridCounts.filter(c => c > 0).length}/9 区域有分布</li>
                        <li>• 中心偏好: {result.gridStatistics.centerPercentage > 50 ? '是' : '否'}</li>
                        <li>• 边缘偏好: {result.gridStatistics.edgePercentage > 50 ? '是' : '否'}</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">评估建议</h4>
                      <ul className="space-y-1 text-gray-600">
                        {result.trackingAccuracy >= 95 && (
                          <li className="text-green-600">✓ 跟踪精度优秀</li>
                        )}
                        {result.trackingAccuracy < 90 && (
                          <li className="text-orange-600">⚠ 跟踪精度偏低</li>
                        )}
                        {result.smoothnessScore >= 95 && (
                          <li className="text-green-600">✓ 眼动轨迹平滑</li>
                        )}
                        {result.smoothnessScore < 90 && (
                          <li className="text-orange-600">⚠ 轨迹不够平滑</li>
                        )}
                      </ul>
                    </div>
                  </div>
                </>
              )}
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}
