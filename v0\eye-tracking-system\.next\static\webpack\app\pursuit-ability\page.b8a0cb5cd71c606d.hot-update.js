"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pursuit-ability/page",{

/***/ "(app-pages-browser)/./components/pursuit-ability-detail.tsx":
/*!***********************************************!*\
  !*** ./components/pursuit-ability-detail.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PursuitAbilityDetail)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/move.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Calendar,Eye,FileText,Gauge,Home,Move,Settings,Target,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction PursuitAbilityDetail(param) {\n    var recordId = param.recordId, onBack = param.onBack;\n    var _record_pursuitAccuracy, _record_averageVelocity, _record_velocityGain, _record_pursuitAccuracy1, _record_averageVelocity1, _record_velocityGain1, _record_phaseDelay, _record_smoothnessIndex, _record_trackingError, _record_pursuitLatency;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_9__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), record = _useState[0], setRecord = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_9__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), pursuitPoints = _useState1[0], setPursuitPoints = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_9__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), loading = _useState2[0], setLoading = _useState2[1];\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    var toast = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)().toast;\n    // 获取详情数据\n    var fetchDetail = /*#__PURE__*/ function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_10__._)(function() {\n            var response, points, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_11__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        setLoading(true);\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            4,\n                            5\n                        ]);\n                        return [\n                            4,\n                            _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].getPursuitAbilityDetail(recordId)\n                        ];\n                    case 2:\n                        response = _state.sent();\n                        setRecord(response.data);\n                        // 解析追随轨迹数据\n                        if (response.data.gazeTrajectoryJson) {\n                            try {\n                                points = JSON.parse(response.data.gazeTrajectoryJson);\n                                // 确保解析的数据是数组\n                                if (Array.isArray(points)) {\n                                    setPursuitPoints(points);\n                                } else {\n                                    console.error('追随轨迹数据不是数组格式:', points);\n                                    setPursuitPoints([]);\n                                }\n                            } catch (error) {\n                                console.error('解析追随轨迹数据失败:', error);\n                                setPursuitPoints([]);\n                            }\n                        } else {\n                            setPursuitPoints([]);\n                        }\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        toast({\n                            title: \"获取详情失败\",\n                            description: error instanceof Error ? error.message : \"请检查网络连接\",\n                            variant: \"destructive\"\n                        });\n                        console.error('获取追随能力测试详情失败:', error);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchDetail() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PursuitAbilityDetail.useEffect\": function() {\n            if (recordId) {\n                fetchDetail();\n            }\n        }\n    }[\"PursuitAbilityDetail.useEffect\"], [\n        recordId\n    ]);\n    // 状态颜色映射\n    var getStatusColor = function(status) {\n        switch(status){\n            case 'COMPLETED':\n                return 'bg-green-100 text-green-800';\n            case 'PROCESSING':\n                return 'bg-blue-100 text-blue-800';\n            case 'FAILED':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    // 格式化时长\n    var formatDuration = function(seconds) {\n        var minutes = Math.floor(seconds / 60);\n        var remainingSeconds = seconds % 60;\n        return \"\".concat(minutes, \":\").concat(remainingSeconds.toString().padStart(2, '0'));\n    };\n    // 返回处理\n    var handleBack = function() {\n        if (onBack) {\n            onBack();\n        } else {\n            router.back();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2\",\n                    children: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, this);\n    }\n    if (!record) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"未找到测试记录\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    onClick: handleBack,\n                    className: \"mt-4\",\n                    children: \"返回列表\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"返回列表\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"追随能力测试详情\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: [\n                                            \"测试序号: \",\n                                            record.testSequence\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                        className: getStatusColor(record.status),\n                        children: record.statusDesc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-base flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"患者信息\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"姓名\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.patientName\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"住院号\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.inpatientNum\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"病历号\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.caseCardNum\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-base flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"测试信息\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"测试日期\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: new Date(record.testDate).toLocaleDateString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"测试时间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: new Date(record.testDate).toLocaleTimeString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"测试时长\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: formatDuration(record.duration)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-base flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"设备信息\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"设备名称\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.deviceName || '未知设备'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"设备序列号\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-xs\",\n                                                children: record.deviceSn\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"操作员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: record.operatorName || '未知'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-base flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"测试结果\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"追随精度\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-green-600\",\n                                                children: [\n                                                    (_record_pursuitAccuracy = record.pursuitAccuracy) === null || _record_pursuitAccuracy === void 0 ? void 0 : _record_pursuitAccuracy.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"平均速度\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    (_record_averageVelocity = record.averageVelocity) === null || _record_averageVelocity === void 0 ? void 0 : _record_averageVelocity.toFixed(1),\n                                                    \" \\xb0/s\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"速度增益\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: (_record_velocityGain = record.velocityGain) === null || _record_velocityGain === void 0 ? void 0 : _record_velocityGain.toFixed(2)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                defaultValue: \"overview\",\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"overview\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"测试概览\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"trajectory\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"追随轨迹\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"settings\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"测试配置\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"notes\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"备注信息\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"overview\",\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-base flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-4 h-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"追随统计\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"总追随时间\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: formatDuration(record.totalPursuitTime)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"有效追随时间\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-green-600\",\n                                                                children: formatDuration(record.effectivePursuitTime)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"追随精度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_pursuitAccuracy1 = record.pursuitAccuracy) === null || _record_pursuitAccuracy1 === void 0 ? void 0 : _record_pursuitAccuracy1.toFixed(1),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-base flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"w-4 h-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"速度分析\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"平均速度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_averageVelocity1 = record.averageVelocity) === null || _record_averageVelocity1 === void 0 ? void 0 : _record_averageVelocity1.toFixed(1),\n                                                                    \" \\xb0/s\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"速度增益\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: (_record_velocityGain1 = record.velocityGain) === null || _record_velocityGain1 === void 0 ? void 0 : _record_velocityGain1.toFixed(2)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"相位延迟\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_phaseDelay = record.phaseDelay) === null || _record_phaseDelay === void 0 ? void 0 : _record_phaseDelay.toFixed(1),\n                                                                    \" ms\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-base flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"w-4 h-4 text-purple-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"质量指标\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"平滑度指数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: (_record_smoothnessIndex = record.smoothnessIndex) === null || _record_smoothnessIndex === void 0 ? void 0 : _record_smoothnessIndex.toFixed(2)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"跟踪误差\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_trackingError = record.trackingError) === null || _record_trackingError === void 0 ? void 0 : _record_trackingError.toFixed(2),\n                                                                    \" px\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"追随延迟\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    (_record_pursuitLatency = record.pursuitLatency) === null || _record_pursuitLatency === void 0 ? void 0 : _record_pursuitLatency.toFixed(1),\n                                                                    \" ms\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this),\n                            record.followPathPoints && record.actualGazePoints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BezierErrorVisualization, {\n                                followPathPoints: record.followPathPoints,\n                                actualGazePoints: record.actualGazePoints,\n                                screenWidth: 1920,\n                                screenHeight: 1080\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"trajectory\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"追随轨迹可视化\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"显示眼球追随目标的运动轨迹和质量分析\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: record.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: record.imageUrl,\n                                                        alt: \"追随轨迹图\",\n                                                        className: \"w-full h-auto\",\n                                                        onError: function(e) {\n                                                            var _e_currentTarget_nextElementSibling;\n                                                            e.currentTarget.style.display = 'none';\n                                                            (_e_currentTarget_nextElementSibling = e.currentTarget.nextElementSibling) === null || _e_currentTarget_nextElementSibling === void 0 ? void 0 : _e_currentTarget_nextElementSibling.classList.remove('hidden');\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"hidden p-8 text-center text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-12 h-12 mx-auto mb-2 text-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"轨迹图像加载失败\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, this),\n                                            pursuitPoints.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                className: \"pb-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                    className: \"text-base\",\n                                                                    children: \"轨迹统计\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"轨迹点数\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 399,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: pursuitPoints.length\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"平均速度\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 403,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    (pursuitPoints.reduce(function(sum, p) {\n                                                                                        return sum + p.velocity;\n                                                                                    }, 0) / pursuitPoints.length).toFixed(1),\n                                                                                    \" \\xb0/s\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"平均误差\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 409,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    (pursuitPoints.reduce(function(sum, p) {\n                                                                                        return sum + p.trackingError;\n                                                                                    }, 0) / pursuitPoints.length).toFixed(2),\n                                                                                    \" px\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                className: \"pb-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                    className: \"text-base\",\n                                                                    children: \"质量分析\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"目标命中率\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 423,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-green-600\",\n                                                                                children: [\n                                                                                    (pursuitPoints.filter(function(p) {\n                                                                                        return p.isOnTarget;\n                                                                                    }).length / pursuitPoints.length * 100).toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 424,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"平均增益\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: (pursuitPoints.reduce(function(sum, p) {\n                                                                                    return sum + p.velocityGain;\n                                                                                }, 0) / pursuitPoints.length).toFixed(2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 430,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"平均延迟\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 435,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    (pursuitPoints.reduce(function(sum, p) {\n                                                                                        return sum + p.phaseDelay;\n                                                                                    }, 0) / pursuitPoints.length).toFixed(1),\n                                                                                    \" ms\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                                lineNumber: 436,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-2 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"暂无轨迹图像\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"settings\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"校准参数\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-sm bg-gray-50 p-3 rounded border overflow-auto\",\n                                                children: record.calibrationParams || '无校准参数'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"环境信息\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-sm bg-gray-50 p-3 rounded border overflow-auto\",\n                                                children: record.environmentInfo || '无环境信息'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"notes\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Calendar_Eye_FileText_Gauge_Home_Move_Settings_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"测试备注\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded border min-h-32\",\n                                        children: record.notes ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm whitespace-pre-wrap\",\n                                            children: record.notes\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"无备注信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\pursuit-ability-detail.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(PursuitAbilityDetail, \"XkreKmfYdf5DHCF8EMyOAbJ7Agw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = PursuitAbilityDetail;\nvar _c;\n$RefreshReg$(_c, \"PursuitAbilityDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pursuit-ability-detail.tsx\n"));

/***/ })

});