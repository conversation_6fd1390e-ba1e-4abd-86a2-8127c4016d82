globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/gaze-stability/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/auth-wrapper.tsx":{"*":{"id":"(ssr)/./components/auth-wrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/pursuit-ability/page.tsx":{"*":{"id":"(ssr)/./app/pursuit-ability/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/bezier-error-evaluation/page.tsx":{"*":{"id":"(ssr)/./app/bezier-error-evaluation/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/gaze-stability/page.tsx":{"*":{"id":"(ssr)/./app/gaze-stability/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/saccade-ability/page.tsx":{"*":{"id":"(ssr)/./app/saccade-ability/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/grid-demo/page.tsx":{"*":{"id":"(ssr)/./app/grid-demo/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\research-ui\\v0\\eye-tracking-system\\components\\auth-wrapper.tsx":{"id":"(app-pages-browser)/./components/auth-wrapper.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\app\\pursuit-ability\\page.tsx":{"id":"(app-pages-browser)/./app/pursuit-ability/page.tsx","name":"*","chunks":[],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\app\\bezier-error-evaluation\\page.tsx":{"id":"(app-pages-browser)/./app/bezier-error-evaluation/page.tsx","name":"*","chunks":[],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\app\\gaze-stability\\page.tsx":{"id":"(app-pages-browser)/./app/gaze-stability/page.tsx","name":"*","chunks":["app/gaze-stability/page","static/chunks/app/gaze-stability/page.js"],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\app\\saccade-ability\\page.tsx":{"id":"(app-pages-browser)/./app/saccade-ability/page.tsx","name":"*","chunks":[],"async":false},"D:\\research-ui\\v0\\eye-tracking-system\\app\\grid-demo\\page.tsx":{"id":"(app-pages-browser)/./app/grid-demo/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\research-ui\\v0\\eye-tracking-system\\":[],"D:\\research-ui\\v0\\eye-tracking-system\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\research-ui\\v0\\eye-tracking-system\\app\\page":[],"D:\\research-ui\\v0\\eye-tracking-system\\app\\gaze-stability\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./components/auth-wrapper.tsx":{"*":{"id":"(rsc)/./components/auth-wrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/pursuit-ability/page.tsx":{"*":{"id":"(rsc)/./app/pursuit-ability/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/bezier-error-evaluation/page.tsx":{"*":{"id":"(rsc)/./app/bezier-error-evaluation/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/gaze-stability/page.tsx":{"*":{"id":"(rsc)/./app/gaze-stability/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/saccade-ability/page.tsx":{"*":{"id":"(rsc)/./app/saccade-ability/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/grid-demo/page.tsx":{"*":{"id":"(rsc)/./app/grid-demo/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}