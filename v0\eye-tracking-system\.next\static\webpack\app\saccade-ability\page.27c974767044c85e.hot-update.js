"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/saccade-ability/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @swc/helpers/_/_class_call_check */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_call_check.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./lib/config.ts\");\n\n\n\n\n\nvar ApiService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function ApiService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, ApiService);\n        var baseURL = (0,_config__WEBPACK_IMPORTED_MODULE_0__.getApiBaseURL)();\n        (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('API Service initialized with baseURL:', baseURL);\n        this.instance = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n            baseURL: baseURL,\n            timeout: _config__WEBPACK_IMPORTED_MODULE_0__.config.api.timeout,\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        // 请求拦截器\n        this.instance.interceptors.request.use(function(config) {\n            var _config_method;\n            (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('API Request:', (_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toUpperCase(), config.url, config.data);\n            // 可以在这里添加认证token等\n            var token = localStorage.getItem('authToken');\n            if (token) {\n                config.headers.Authorization = \"Bearer \".concat(token);\n            }\n            return config;\n        }, function(error) {\n            (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('API Request Error:', error);\n            return Promise.reject(error);\n        });\n        // 响应拦截器\n        this.instance.interceptors.response.use(function(response) {\n            (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('API Response:', response.status, response.config.url, response.data);\n            var data = response.data;\n            if (data.code === 200) {\n                return response;\n            } else {\n                // 处理业务错误\n                (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('API Business Error:', data);\n                throw new Error(data.message || '请求失败');\n            }\n        }, function(error) {\n            (0,_config__WEBPACK_IMPORTED_MODULE_0__.debugLog)('API Response Error:', error);\n            // 处理HTTP错误\n            if (error.response) {\n                var _error_response = error.response, status = _error_response.status, data = _error_response.data;\n                switch(status){\n                    case 401:\n                        // 未授权，跳转到登录页\n                        if (true) {\n                            window.location.href = '/login';\n                        }\n                        break;\n                    case 403:\n                        throw new Error('没有权限访问');\n                    case 404:\n                        throw new Error('请求的资源不存在');\n                    case 500:\n                        throw new Error('服务器内部错误');\n                    default:\n                        throw new Error((data === null || data === void 0 ? void 0 : data.message) || '请求失败');\n                }\n            } else if (error.request) {\n                throw new Error('网络连接失败');\n            } else {\n                throw new Error('请求配置错误');\n            }\n            return Promise.reject(error);\n        });\n    }\n    var _proto = ApiService.prototype;\n    // 通用GET请求\n    _proto.get = function get(url, config) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            var response;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            _this.instance.get(url, config)\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        return [\n                            2,\n                            response.data\n                        ];\n                }\n            });\n        })();\n    };\n    // 通用POST请求\n    _proto.post = function post(url, data, config) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            var response;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            _this.instance.post(url, data, config)\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        return [\n                            2,\n                            response.data\n                        ];\n                }\n            });\n        })();\n    };\n    // 通用PUT请求\n    _proto.put = function put(url, data, config) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            var response;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            _this.instance.put(url, data, config)\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        return [\n                            2,\n                            response.data\n                        ];\n                }\n            });\n        })();\n    };\n    // 通用DELETE请求\n    _proto[\"delete\"] = function _delete(url, config) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            var response;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            _this.instance[\"delete\"](url, config)\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        return [\n                            2,\n                            response.data\n                        ];\n                }\n            });\n        })();\n    };\n    // 注视稳定性测试相关API\n    // 获取注视稳定性测试列表（分页）\n    _proto.getGazeStabilityList = function getGazeStabilityList(params) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/gaze-stability/page', params)\n                ];\n            });\n        })();\n    };\n    // 获取注视稳定性测试详情\n    _proto.getGazeStabilityDetail = function getGazeStabilityDetail(recordId) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.get(\"/api/movement/gaze-stability/record/\".concat(recordId))\n                ];\n            });\n        })();\n    };\n    // 创建注视稳定性测试\n    _proto.createGazeStabilityTest = function createGazeStabilityTest(data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/gaze-stability', data)\n                ];\n            });\n        })();\n    };\n    // 更新注视稳定性测试\n    _proto.updateGazeStabilityTest = function updateGazeStabilityTest(id, data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.put(\"/api/movement/gaze-stability/\".concat(id), data)\n                ];\n            });\n        })();\n    };\n    // 删除注视稳定性测试\n    _proto.deleteGazeStabilityTest = function deleteGazeStabilityTest(id) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this[\"delete\"](\"/api/movement/gaze-stability/\".concat(id))\n                ];\n            });\n        })();\n    };\n    // 扫视能力测试相关API\n    // 获取扫视能力测试列表（分页）\n    _proto.getSaccadeAbilityList = function getSaccadeAbilityList(params) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/saccade-ability/page', params)\n                ];\n            });\n        })();\n    };\n    // 获取扫视能力测试详情\n    _proto.getSaccadeAbilityDetail = function getSaccadeAbilityDetail(recordId) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.get(\"/api/movement/saccade-ability/record/\".concat(recordId))\n                ];\n            });\n        })();\n    };\n    // 创建扫视能力测试\n    _proto.createSaccadeAbilityTest = function createSaccadeAbilityTest(data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/saccade-ability', data)\n                ];\n            });\n        })();\n    };\n    // 更新扫视能力测试\n    _proto.updateSaccadeAbilityTest = function updateSaccadeAbilityTest(id, data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.put(\"/api/movement/saccade-ability/\".concat(id), data)\n                ];\n            });\n        })();\n    };\n    // 删除扫视能力测试\n    _proto.deleteSaccadeAbilityTest = function deleteSaccadeAbilityTest(id) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this[\"delete\"](\"/api/movement/saccade-ability/\".concat(id))\n                ];\n            });\n        })();\n    };\n    // 追随能力测试相关API\n    // 获取追随能力测试列表（分页）\n    _proto.getPursuitAbilityList = function getPursuitAbilityList(params) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/pursuit-ability/page', params)\n                ];\n            });\n        })();\n    };\n    // 获取追随能力测试详情\n    _proto.getPursuitAbilityDetail = function getPursuitAbilityDetail(recordId) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.get(\"/api/movement/pursuit-ability/record/\".concat(recordId))\n                ];\n            });\n        })();\n    };\n    // 创建追随能力测试\n    _proto.createPursuitAbilityTest = function createPursuitAbilityTest(data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.post('/api/movement/pursuit-ability', data)\n                ];\n            });\n        })();\n    };\n    // 更新追随能力测试\n    _proto.updatePursuitAbilityTest = function updatePursuitAbilityTest(id, data) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this.put(\"/api/movement/pursuit-ability/\".concat(id), data)\n                ];\n            });\n        })();\n    };\n    // 删除追随能力测试\n    _proto.deletePursuitAbilityTest = function deletePursuitAbilityTest(id) {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_3__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_4__.__generator)(this, function(_state) {\n                return [\n                    2,\n                    _this[\"delete\"](\"/api/movement/pursuit-ability/\".concat(id))\n                ];\n            });\n        })();\n    };\n    return ApiService;\n}();\n// 创建API服务实例\nvar apiService = new ApiService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});